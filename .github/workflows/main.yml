name: build and deploy
on:
  push:
    branches:
      - develop
      - uat
      - master
jobs:
  call_build:
    uses: c2c-techhub/cicd/.github/workflows/auto-build-with-artifact.yml@main
    secrets: inherit
    with:
      environment_for_build: ${{ github.ref == 'refs/heads/master' && 'buildproduction' || github.ref == 'refs/heads/uat' && 'builduat' || 'builddevelopment' }}
      artifact_name: 'build-files'
      output_of_build: "build"
      nodejs_version: "16.17.0"
      bash_command_for_build: |
        sudo npm install -g yarn
        yarn
        unset CI  
        export NODE_OPTIONS=--max-old-space-size=6144
        yarn run build
  call_workflow:
    needs: call_build
    uses: c2c-techhub/cicd/.github/workflows/auto-deploy.yml@main
    with:
      environment_for_deploy: ${{ github.ref == 'refs/heads/master' && 'production' || github.ref == 'refs/heads/uat' && 'uat' || 'development' }}
      artifact_name: 'build-files'
      deployment_type: 'cloudfront'
      enable_auto_deploy: true
      enable_auto_build: false
    secrets: inherit