image: node:16-alpine
definitions:
  steps:
    - step: &build
        name: build
        script:
          - apk add --no-cache git && apk add --no-cache openssh
          - yarn
          - unset CI        # Temporary fix for https://bitbucket.org/hidesignsJP/theplace-admin/addon/pipelines/home#!/results/5/steps/%7Be524c125-04b3-46c3-bd65-cf11d4788032%7D
          - export NODE_OPTIONS=--max-old-space-size=6144
          - yarn run build
        size: 2x
        caches:
          - node
        artifacts:
          - build/**
    - step: &lint
        name: lint
        script:
          - apk add --no-cache git && apk add --no-cache openssh
          - yarn
          - yarn run lint || true
        size: 2x
        caches:
          - node
    - step: &deploy
        name: deploy
        image: public.ecr.aws/j9v6w7g7/auto-deploy-image:stable
        script:
          - apk add curl
          - auto_deploy cloudfront


pipelines:
  branches:
    master:
      - step:
          <<: *build
          deployment: BuildProduction
      - step:
          <<: *deploy
          deployment: Production
          trigger: manual
    uat:
      - step:
          <<: *build
          deployment: BuildUAT
      - step:
          <<: *deploy
          deployment: UAT
    develop:
      - parallel:
        - step: *lint
        - step:
            <<: *build
            deployment: BuildDevelopment
      - step:
          <<: *deploy
          deployment: Development
          image: public.ecr.aws/j9v6w7g7/auto-deploy-image:latest
