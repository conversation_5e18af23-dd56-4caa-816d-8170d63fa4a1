{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "downlevelIteration": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "removeComments": true, "preserveConstEnums": true, "strict": true, "alwaysStrict": true, "strictNullChecks": true, "noUncheckedIndexedAccess": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "allowUnreachableCode": false, "noFallthroughCasesInSwitch": true, "target": "es5", "outDir": "out", "declaration": true, "sourceMap": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "allowJs": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "jsx": "preserve", "noEmit": true, "isolatedModules": true, "incremental": true, "baseUrl": "./src", "plugins": [{"name": "next"}], "paths": {"@icons/*": ["../public/icons/*"], "@images/*": ["../public/images/*"]}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules, 'tsconfig.jsonyarn"]}