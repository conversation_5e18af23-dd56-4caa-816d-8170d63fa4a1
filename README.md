# Introduction of Minimal Admin Template

Minimal Admin Template is a template project for admin dashboard in our projects. We are focus on three main keys here.

- **Fast** - Easy and fast to start/implement for your dashboard task.
- **Maintainability** - Easy to maintain even other developer who join in your project after launched.
- **Simple** - No complicate or strong dependencies components in this template. Any React developer can use.

## Features

What we are providing in this template.

- Authentication logic for C2C backend server (Not Auth0)
- Private/Public routes for every page
- Common dashboard UI such as left and header menu, tables, forms, toast for success/error and others.
- Custom hooks for calling APIs or manage your global states.

## Installed libraries

- React - v18.2
- TypeScript - v5.0

**StateManagement**

- TanStack v4.27 - For Server state (https://tanstack.com/)
- Zustand v4.3 - For global state (https://www.npmjs.com/package/zustand)

**UI**

- Ant-design v5.4 - UI components (https://www.npmjs.com/package/antd)
- Tiptap - The headless editor (https://tiptap.dev/)
- Recharts - Chart library (https://recharts.org/en-US/)

**CSS**

- tailwindcss - CSS library (https://tailwindcss.com/)
- postcss - transforming styles with JS plugins (https://postcss.org/)

**Others**

- axios - Promise based HTTP client for the browser and node.js (https://www.npmjs.com/package/axios)
- cookies-next - cookies used in APIs (https://www.npmjs.com/package/cookies-next)
- dayjs - displays dates and times (https://www.npmjs.com/package/dayjs)
- lodash - utilities (https://www.npmjs.com/search?q=lodash)
- react-hook-form - Forms library for Hooks (https://www.npmjs.com/package/react-hook-form)
- react-number-format - Input format library (https://www.npmjs.com/package/react-number-format)
- i18n-js - Translate library (https://www.npmjs.com/package/i18n-js)
- Yup - Object schema validation (https://www.npmjs.com/package/yup)
- Prettier - Code formatter (https://prettier.io/)
- Query-string - parsing and handling query string for router (https://www.npmjs.com/package/query-string)
- react-router - router control (https://www.npmjs.com/package/react-router-dom)

## Installation

Please use Node v16 (lts/gallium) or above. Also please use yarn for installing packages.

1. Install packages.

```
yarn install
```

2. Create environment variable.

```
cp .env.example .env
```

3. Change commit message pattern in `commitlint.config.js`. You can check here for [commit message rules](#Commit-message).
4. Start vite server on port 3000

```
yarn start
```

## Usage

We are providing some example containers in `src/containers/Example`. This example will give you some usage about creating common pages such as List, Detail and Edit with regular/modal page. After you get idea about this template usage please delete this Example containers.

### How to create list page

1. Create models in `src/models`. Add routes for get list page endpoint. And add type also.
2. Create container in `src/containers`. List page should be inside `/List` directory. Named index.tsx.
3. To fetch from API, use custom hook `src/hooks/useList.ts`.
4. Add `PageContainer` from Ant design pro-layout and add `CustomTable` from our `src/components/CustomTable/index.tsx`. CustomeTable is based on Ant design pro-table.
5. Make sure you separate your columns into `columns.ts` file. This will help your code much more readable and cleaner.
6. Pass `list, isFetching, pagination` from `useList` hooks and pass it to CustomTable component.
7. Add List components in `src/router/adminRoutes.tsx`

**Here's if you need search component in your list page.**

8. Create `yup` schema in `List/searchSchema.ts`. This is for validation for keyword objects.
9. Add `src/components/SearchWrapper` and `src/components/Form/TextInput` in PageContainer at list page.

### How to create detail page

1. Add detail function in models `src/models`
2. Create Detail container in `src/containers/YourContainerName/Detail/index.tsx`
3. Use `CustomDescription` for your detail components.
4. Add Detail components in `src/router/adminRoutes.tsx`
5. Wrap with Suspense component if you want to modify your suspense style.

### How to create edit page

1. Add update function in model `src/models/example/index.ts`
2. Create InputForm component directory. `src/containers/Example/ExampleInputForm`
3. Create schema. This is schema for all the input fields. `src/containers/Example/ExampleInputForm/schema.ts`
4. Create Example form component `src/containers/Example/ExampleInputForm/index.tsx`.
5. Create Edit component directory and create index.tsx `src/containers/Example/Edit/index.tsx`
6. Add Edit component route into `src/router/adminRoutes.tsx`.
7. Add Link button in `src/containers/Example/Detail/index.tsx`.

### How to create detail/modal components
1. Create hooks for detail/edit modal. Here's example `src/containers/Example/Edit/hooks/useExampleModalEdit.tsx.`
2. Call this hooks from where ever you want to call.

## Commit message

1. Compulsory follow format **`type(scope): jira-issue Message`**
2. **`type`**: One of the types **`['chore', 'feat', 'fix', 'perf', 'refactor']`**
3. **`(scope)`**: Optional, accept all characters. If you don't fill in the **`scope`**, remove the sign **`( )`**.
4. **`jira-issue`**: YourProjectShortName-XXXX, ex:V00600HUGO-001, if no tags, 000, you can declare multiple tags in the same commit separated by spaces.
5. **`Message`**: Mandatory capital letter at the beginning of sentence. You can reuse the words in the **`type`**.
6. Between **`type(scope)`** and **`jira-issue`** must have a **`:`** and a space.
7. There must be a space between **`jira-issue`** and **`Message`**.
8. **Special**: To make the commit not appear in Changelog, add tag **`[skipclog]`** at the end of **`Message`** Ex: “fix(scope): V00600HUGO-000 Fix something [skipclog]”

