import { useLocation } from 'react-router';
import { Navigate } from 'react-router-dom';
import Helper from 'utils/helpers';

export interface PrivateRouteProps {
  children: JSX.Element;
}

const PrivateRoute = ({ children }: PrivateRouteProps) => {
  const webCookie = Helper.getWebCookie();
  const { pathname } = useLocation();
  if (!webCookie?.token) {
    Helper.removeWebCookie();
    return <Navigate replace state={{ redirect: pathname }} to="/login" />;
  }
  return children;
};

export default PrivateRoute;
