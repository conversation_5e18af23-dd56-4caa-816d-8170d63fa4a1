/* eslint-disable simple-import-sort/imports */
import { AccountBookOutlined, UsergroupAddOutlined, ScheduleOutlined } from '@ant-design/icons';
import { Navigate } from 'react-router';
// import ExampleList from 'containers/Example/List';
import ExampleDetail from 'containers/Example/Detail';
import ExampleEdit from 'containers/Example/Edit';
import TherapistList from 'containers/Therapist/List';
import SalonSettingDetail from 'containers/SalonOwner/SalonSettingDetail';
import BasicInfo from 'containers/SalonOwner/SalonSettingDetail/Profile';
import BankInfo from 'containers/SalonOwner/SalonSettingDetail/BankInfo';
import TherapistDetail from 'containers/Therapist/Detail';
import TherapistProfile from 'containers/Therapist/Detail/TherapistProfile';
import TherapistReview from 'containers/Therapist/Detail/Reviews';
import BlackListCustomer from 'containers/Therapist/Detail/BlockedCustomer';
import SlotSchedule from 'containers/Therapist/Detail/SlotSchedule';
import NotesForm from 'containers/Therapist/Detail/Notes';
import BookingList from 'containers/Booking/List';
import BookingDetail from 'containers/Booking/Detail';
import SchedulePage from 'containers/Schedules';

const adminRoutes = [
  {
    path: 'booking',
    name: 'sideMenu:reservationManagement',
    icon: <AccountBookOutlined />,
    element: <BookingList />,
  },
  {
    path: 'therapist',
    name: 'sideMenu:therapistManagement',
    icon: <UsergroupAddOutlined />,
    element: <TherapistList />,
  },
  {
    path: 'therapist/:id',
    element: <TherapistDetail />,
    children: [
      {
        path: 'profile-info',
        element: <TherapistProfile />,
      },
      {
        path: 'schedule',
        element: <SlotSchedule />,
      },
      {
        path: 'review',
        element: <TherapistReview />,
      },
      {
        path: 'note',
        element: <NotesForm />,
      },
      {
        path: 'blocked-customer',
        element: <BlackListCustomer />,
      },
    ],
  },
  {
    path: 'booking/:id',
    element: <BookingDetail />,
  },
  {
    path: 'schedule',
    name: '稼働スケジュール',
    icon: <ScheduleOutlined />,
    element: <SchedulePage />,
  },
  {
    path: 'profile',
    hideInMenu: true,
    element: <SalonSettingDetail />,
    children: [
      {
        path: 'setting',
        element: <BasicInfo />,
      },
      {
        path: 'bank-info',
        element: <BankInfo />,
      },
    ],
  },
  // {
  //   path: 'example',
  //   name: 'Example',
  //   icon: <HomeOutlined />,
  //   element: <ExampleList />,
  // },
  {
    path: 'example/:id',
    element: <ExampleDetail />,
  },
  {
    path: 'example/:id/edit',
    element: <ExampleEdit />,
  },
  {
    path: '',
    element: <Navigate replace to="/booking" />,
  },
];

export default adminRoutes;
