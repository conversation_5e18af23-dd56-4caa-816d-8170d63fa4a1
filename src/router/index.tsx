import Root from 'containers';
import ExpiredTokenPage from 'containers/ExpiredTokenScreen';
import PageLayout from 'containers/Layout';
import Login from 'containers/Login';
import NotFoundPage from 'containers/NotFoundPage';
import ChangeEmailVerify from 'containers/SalonOwner/ChangeEmail/ChangeEmailVerify';
import SalonCompleteProfile from 'containers/SalonOwner/CompleteProfile';
import ResetPasswordPage from 'containers/SalonOwner/ForgotPassword';
import RequestForgotPassword from 'containers/SalonOwner/RequestForgotPassword';
import SentEmailNotificationPage from 'containers/SalonOwner/SentEmailNotification';
import type { RouteObject } from 'react-router';
import { createBrowserRouter } from 'react-router-dom';

import adminRoutes from './adminRoutes';
import PrivateRoute from './PrivateRoutes';
import PublicRoute from './PublicRoutes';
import type { AntRoute } from './types';

export const indexRoutes: RouteObject[] = [
  {
    path: '/',
    element: <Root />,
    children: [
      {
        path: 'login',
        element: (
          <PublicRoute>
            <Login />
          </PublicRoute>
        ),
      },
      {
        path: '/activate-account',
        element: (
          <PublicRoute>
            <SalonCompleteProfile />
          </PublicRoute>
        ),
      },
      {
        path: '/request-forgot-password',
        element: (
          <PublicRoute>
            <RequestForgotPassword />
          </PublicRoute>
        ),
      },
      {
        path: '/verify-change-email',
        element: (
          <PublicRoute>
            <ChangeEmailVerify />
          </PublicRoute>
        ),
      },
      {
        path: '/sent-email-success',
        element: (
          <PublicRoute>
            <SentEmailNotificationPage />
          </PublicRoute>
        ),
      },
      {
        path: '/url-expired',
        element: (
          <PublicRoute>
            <ExpiredTokenPage />
          </PublicRoute>
        ),
      },
      {
        path: '/forgot-password',
        element: (
          <PublicRoute>
            <ResetPasswordPage />
          </PublicRoute>
        ),
      },
      {
        path: '',
        errorElement: <NotFoundPage />,
        element: (
          <PrivateRoute>
            <PageLayout />
          </PrivateRoute>
        ),
        children: adminRoutes,
      },
      { path: '*', element: <NotFoundPage /> },
    ],
  },
];

const routes = [...indexRoutes, ...adminRoutes];

export const convertToMenuRoutes = (routeArr: (RouteObject & AntRoute)[]): AntRoute[] => {
  return routeArr.map((route) => ({
    ...route,
    ...(Array.isArray(route.children) ? { routes: convertToMenuRoutes(route.children) } : {}),
  }));
};

export const sideMenuRoutes = convertToMenuRoutes(adminRoutes);

export default routes;

export const router = createBrowserRouter(routes);
