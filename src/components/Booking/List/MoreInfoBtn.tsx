import { Button, Typography } from 'antd';
import type { IBookingListItem } from 'models/booking/types';
import React from 'react';
import { useLocation, useNavigate } from 'react-router';

const { Paragraph } = Typography;

const MoreInfoBtn = ({ data }: { data: IBookingListItem }) => {
  const { pathname } = useLocation();
  const navigate = useNavigate();
  return (
    <Paragraph style={{ marginBottom: 0 }}>
      <Button
        onClick={() => {
          navigate(`${pathname}/${data?._id}`);
        }}
        size="middle"
        type="primary"
      >
        詳細
      </Button>
    </Paragraph>
  );
};

export default MoreInfoBtn;
