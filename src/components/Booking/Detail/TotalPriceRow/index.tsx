import './style.css';

import { CheckCircleIcon, IconAmex, IconMaster, IconVisa } from 'icons';
import { capitalize, get, kebabCase } from 'lodash';
import type { IPayment, IPoint } from 'models/booking/types';
import { Fragment } from 'react';
import { TYPE_PAYMENT } from 'utils/constants';
import formatCurrencyJPY from 'utils/number';

type Props = {
  payment: IPayment;
  totalPrice: number;
  totalDuration: number;
  point: IPoint;
};
const PAYMENT_ICON = {
  'american-express': <IconAmex />,
  'master-card': <IconMaster />,
  'visa': <IconVisa />,
};
const TotalPriceRow = ({ payment, totalPrice, totalDuration, point }: Props) => {
  return (
    <div className="menu-total w-full md:max-w-2xl">
      <div className="menu-total__inner clearfix">
        <div className="flex justify-between items-center">
          <b>合計</b>
          <strong className="pull-right menu-total__price">{formatCurrencyJPY(totalPrice)}</strong>
        </div>
        <div className="flex justify-between items-center">
          <small className="flex items-center">
            クレジットカード &nbsp;
            {[TYPE_PAYMENT.CARD, TYPE_PAYMENT.GCARD, TYPE_PAYMENT.VCARD].includes(payment?.type) ? (
              <Fragment>
                {
                  PAYMENT_ICON[
                    kebabCase(
                      get(payment, 'transaction.extra.source.brand'),
                    ) as keyof typeof PAYMENT_ICON
                  ]
                }
                &nbsp;X
                {get(payment, 'transaction.extra.source.last4')}
              </Fragment>
            ) : (
              capitalize(payment ? payment.type : TYPE_PAYMENT.CASH)
            )}
          </small>
          <small>時間： {totalDuration} 分</small>
        </div>
      </div>
      {point?.used?.isUsed === false && (
        <div className="container-booking-paid-status done outline-none">
          <CheckCircleIcon /> 使用されたポイントが返済しました
        </div>
      )}
    </div>
  );
};

export default TotalPriceRow;
