import { Row, Space } from 'antd';
import type { ReactNode } from 'react';
import React from 'react';

type Props = {
  className?: string;
  label: ReactNode;
  value: ReactNode;
  labelMinWidth?: string | number;
};

const BookingFieldValue = ({ label, value, className, labelMinWidth }: Props) => (
  <Row align={'top'} className={`${className} pb-6 last:pb-0 `}>
    <Space className="h-full !items-start" size={8}>
      <div
        className={`${
          labelMinWidth || labelMinWidth === 0 ? `min-w-[${labelMinWidth}]` : 'min-w-[200px]'
        } h-full`}
      >
        <div className="text-[#74788b] pb-1 font-medium">{label}</div>
      </div>
      <div className="text-[#080c35] font-semibold">{value || '-'}</div>
    </Space>
  </Row>
);

export default BookingFieldValue;
