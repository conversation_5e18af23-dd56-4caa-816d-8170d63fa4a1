import { Avatar, Space } from 'antd';
import { MidNightIcon } from 'icons';
import formatCurrencyJPY from 'utils/number';

type Props = {
  amount: number | null;
};

const MidNightFee = ({ amount = 0 }: Props) => {
  return (
    <div className="w-full md:max-w-2xl bg-[#9d7ec71a] p-2 text-[#9d7ec7]">
      <div className="flex justify-between">
        <Space size="middle">
          <Avatar size="large" src={<MidNightIcon />} />
          <div className="font-medium text-base">深夜料金</div>
        </Space>
        <div className="flex items-center text-base font-bold ">
          {amount && amount > 0 && formatCurrencyJPY(amount)}
        </div>
      </div>
    </div>
  );
};

export default MidNightFee;
