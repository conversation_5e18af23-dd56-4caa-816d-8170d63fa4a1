import { Tag, Typography } from 'antd';
import formatCurrencyJPY from 'utils/number';

const { Text } = Typography;
type Props = {
  amount: number;
  code: string;
};

const Coupon = ({ amount = 0, code = '' }: Props) => {
  return (
    <div className="w-full md:max-w-2xl bg-[#E4EBEF] p-4 pr-2">
      <div className="flex justify-between">
        <Tag className="text-sm" color="#A6B5BF">
          {code}
        </Tag>
        <Text className="text-base font-semibold">-{formatCurrencyJPY(amount)}</Text>
      </div>
    </div>
  );
};

export default Coupon;
