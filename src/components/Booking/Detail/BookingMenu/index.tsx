import './style.css';

import { Avatar, Empty, List, Space } from 'antd';
import type { ExtensionBooking, IBookingMenu } from 'models/booking/types';
import formatCurrencyJPY from 'utils/number';

type Props = {
  bookingMenu: IBookingMenu[];
  extensions: ExtensionBooking[];
  totalPrice: number;
  totalDuration: number;
};

const BookingMenu = ({ bookingMenu, extensions }: Props) => {
  return (
    <div>
      {bookingMenu?.length === 0 ? (
        '対応可能メニューがありません'
      ) : (
        <List
          className="max-h-[320px] overflow-auto "
          dataSource={bookingMenu}
          itemLayout="vertical"
          renderItem={(data) => (
            <List.Item
              className="max-h-[63px] !p-2 !mb-2 bg-[#E4EBEF]"
              extra={
                <div className="font-semibold">
                  {formatCurrencyJPY(data?.selectedOption?.price || 0)}
                </div>
              }
              key={data._id}
            >
              <List.Item.Meta
                avatar={
                  <Avatar
                    icon={<Empty />}
                    size="large"
                    src={data.images && data.images.icon && data.images.icon.url}
                    style={{ backgroundColor: 'transparent' }}
                  />
                }
                className="max-h-[63px]"
                description={
                  <div className="duration-menu">
                    <Space size={8}>
                      <small>{data?.selectedOption?.duration}&nbsp;分</small>
                      <small>
                        {data?.selectedOption?.price}
                        &nbsp;{data?.selectedOption?.currency}
                      </small>
                    </Space>
                  </div>
                }
                title={<div className="text-sm ">{data?.title}</div>}
              />
            </List.Item>
          )}
          size="small"
        />
      )}
      {extensions?.length > 0 && (
        <div className="extensions-title mt-4">
          <span>延長メニュー</span>
        </div>
      )}
      {extensions?.length > 0 && (
        <List
          className="max-h-[320px] overflow-auto "
          dataSource={extensions}
          itemLayout="vertical"
          renderItem={(data) => (
            <List.Item
              className="max-h-[63px] !p-2 !mb-2 bg-[#E4EBEF]"
              extra={
                <div className="font-semibold">
                  {formatCurrencyJPY(data?.selectedExtension?.price || 0)}
                </div>
              }
              key={data._id}
            >
              <List.Item.Meta
                avatar={
                  <Avatar
                    icon={<Empty />}
                    size="large"
                    src={data.images && data.images.icon && data.images.icon.url}
                    style={{ backgroundColor: 'transparent' }}
                  />
                }
                className="max-h-[63px]"
                description={
                  <div className="duration-menu">
                    <Space size={8}>
                      <small>{data?.selectedExtension?.duration}&nbsp;分</small>
                      <small>
                        {data?.selectedExtension?.price}
                        &nbsp;{data?.selectedExtension?.currency}
                      </small>
                    </Space>
                  </div>
                }
                title={<div className="text-sm ">{data?.title}</div>}
              />
            </List.Item>
          )}
          size="small"
        />
      )}
    </div>
  );
};

export default BookingMenu;
