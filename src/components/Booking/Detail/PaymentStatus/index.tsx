import './style.css';

import { REASON, STATUS, TRANSACTION_STATUS } from 'containers/Booking/Detail/function';
import { ChargeErrorIcon, ChargePaidIcon, PresentIcon, TriagleExclamationIcon } from 'icons';
import type { IBooking } from 'models/booking/types';
import formatCurrencyJPY from 'utils/number';

type Props = {
  selectedBooking: IBooking;
};

const GrantedPointRow = ({ point }: { point?: number }) => (
  <div className="container-booking-paid-status warn justify-between">
    <span className="flex">
      <PresentIcon />
      <span className="ml-1">獲得ポイント数</span>
    </span>
    <span>+{point}</span>
  </div>
);

const PaymentStatus = ({ selectedBooking }: Props) => {
  const transaction = selectedBooking?.payment?.transaction;
  const currentStatus = selectedBooking?.currentStatus?.status;
  const currentStatusReason = selectedBooking?.currentStatus?.reason;

  const isPaymentFailed =
    (transaction.status === TRANSACTION_STATUS.UNCAPTURED ||
      transaction.status === TRANSACTION_STATUS.DRAFT) &&
    selectedBooking?.currentStatus?.requestBy !== 'system';

  const isPaid =
    selectedBooking?.cancellingNote?.isCharged &&
    currentStatus === STATUS.CANCELED &&
    transaction.status !== TRANSACTION_STATUS.CANCELED;

  const isDone = currentStatus === STATUS.DONE;
  const isGrantedPoint =
    transaction.status === TRANSACTION_STATUS.CAPTURED && !!selectedBooking?.point?.granted?.point;

  if (isPaid) {
    return (
      <div className="container-booking-paid">
        <div className="container-booking-canceled-paid-info">
          キャンセル料{formatCurrencyJPY(transaction?.amount || 0)}
        </div>
        <div className="container-booking-canceled-paid-description">キャンセル料が発生します</div>
        {isPaymentFailed ? (
          <div className="container-booking-paid-status failed">
            <ChargeErrorIcon /> Settlement error
          </div>
        ) : (
          <div className="container-booking-paid-status done">
            <ChargePaidIcon /> 支払い完了
          </div>
        )}
        {isGrantedPoint && <GrantedPointRow point={selectedBooking?.point?.granted?.point} />}
      </div>
    );
  }
  if (isDone) {
    return (
      <div className="container-booking-paid">
        {currentStatusReason === REASON.FINISH_WITHOUT_TREATMENT && (
          <div className="container-booking-canceled-paid-description flex items-center gap-x-1">
            <div className="h-4 w-4">
              <TriagleExclamationIcon />
            </div>
            未施術での決済完了
          </div>
        )}
        <div className="container-booking-paid-status done">
          <ChargePaidIcon />
          支払い完了
        </div>
        {isGrantedPoint && <GrantedPointRow point={selectedBooking?.point?.granted?.point} />}
      </div>
    );
  }

  return null;
};

export default PaymentStatus;
