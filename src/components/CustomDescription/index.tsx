import type { DescriptionsProps } from 'antd';
import { Descriptions, Skeleton } from 'antd';
import TextShowMore from 'components/TextShowMore';
import { get } from 'lodash';
import type { ReactNode } from 'react';
import type { Path } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

export interface DescriptionFields<T> {
  label: string;
  path?: Path<T>;
  render?: (record: T) => ReactNode;
  span?: number;
  type?: 'paragraph';
}

const CustomDescription = <T extends object>({
  fields = [],
  data,
  layout,
  ...props
}: {
  data?: T;
  fields: DescriptionFields<T>[];
  layout?: 'horizontal' | 'vertical';
} & DescriptionsProps) => {
  const { t } = useTranslation();

  const renderContent = (record: T, field: DescriptionFields<T>) => {
    if (field.render) {
      return field.render(record) || '---';
    }
    if (field.path) {
      if (field.type === 'paragraph') {
        return <TextShowMore content={(get(record, field.path) as string) || '---'}></TextShowMore>;
      }
      return get(record, field.path as never, '---');
    }
    return null;
  };

  return (
    <Descriptions {...props} colon={false} column={24} layout={layout} size="small">
      {fields.map((field) => (
        <Descriptions.Item
          contentStyle={{ whiteSpace: 'pre-line', marginBottom: 12 }}
          key={field.path || field.label}
          label={t(field.label)}
          labelStyle={{ fontWeight: 'bold', color: '#333', fontSize: 16 }}
          span={field.span || 12}
          style={{ paddingBottom: 0 }}
        >
          {data ? renderContent(data, field) : <Skeleton.Input active size="small" />}
        </Descriptions.Item>
      ))}
    </Descriptions>
  );
};

export default CustomDescription;
