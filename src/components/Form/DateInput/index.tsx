import { DatePicker as AntdDatePicker, Form } from 'antd';
import type { PickerBaseProps } from 'antd/es/date-picker/generatePicker';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';
import { DateFormat } from 'utils/constants';

import HelperText from '../HelperText';

export interface DateInputProps<TFormValues extends FieldValues>
  extends Omit<PickerBaseProps<Dayjs>, 'picker'> {
  label?: string;
  className?: string;
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  format?: string;
  picker?: 'date' | 'week' | 'month' | 'quarter' | 'year';
  required?: boolean;
  helperText?: string;
}

const DateInput = <TFormValues extends FieldValues>({
  label,
  name,
  className = '',
  format = DateFormat.YEAR_MONTH_DATE,
  picker,
  control,
  required,
  helperText,
  ...props
}: DateInputProps<TFormValues>) => {
  const {
    field: { value, onChange, ...otherFields },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const handleOnChange = (date: Dayjs | null) => {
    onChange(date ? date.toISOString() : null);
  };

  return (
    <Form.Item
      className={className}
      colon={false}
      help={<HelperText error={error?.message} helperText={helperText} />}
      label={label}
      required={required}
      validateStatus={error ? 'error' : 'success'}
    >
      <AntdDatePicker
        {...otherFields}
        format={format}
        inputReadOnly={true}
        onChange={handleOnChange}
        picker={picker}
        showToday={false}
        value={value ? dayjs(value) : null}
        {...props}
      />
    </Form.Item>
  );
};

export default DateInput;
