import './style.css';

import type { FormItemProps } from 'antd';
import { Form } from 'antd';
import type { CountryPhoneInputProps, CountryPhoneInputValue } from 'antd-country-phone-input';
import CountryPhoneInput from 'antd-country-phone-input';
import type { ReactNode } from 'react';
import { useId, useState } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import HelperText from '../HelperText';

export interface PhoneInputProps<TFormValues extends FieldValues> extends CountryPhoneInputProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: ReactNode;
  className?: string;
  formItemProps?: FormItemProps;
  required?: boolean;
  helperText?: string;
}

const PhoneInput = <TFormValues extends FieldValues>({
  label,
  className = '',
  control,
  name,
  formItemProps,
  required,
  maxLength,
  helperText,
  ...props
}: PhoneInputProps<TFormValues>) => {
  const id = useId();
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });
  const [countryPhoneValue, setContryPhoneValue] = useState<CountryPhoneInputValue>({
    code: 81,
    phone: '',
    short: 'JP',
  });
  const { value, onChange, ...fieldRest } = field;
  const { onChange: customOnchange, ...otherProps } = props;

  const handleInputChange = (values: CountryPhoneInputValue) => {
    const { code, phone, short } = values;
    // Remove non-numeric characters
    const numericValue = phone ? phone.replace(/[^0-9]/g, '') : '';
    // Limit the input to 12 numbers
    const limitedValue = numericValue.slice(0, 12);
    // Update the field value
    let formattedValue = limitedValue;
    // Remove leading '0' from the phone number
    formattedValue = limitedValue.replace(/^0+/, '');

    setContryPhoneValue({ code, phone: limitedValue, short });
    if (customOnchange) {
      customOnchange({ code, phone: formattedValue, short });
    } else {
      onChange(formattedValue);
    }
  };

  return (
    <Form.Item
      className={className}
      colon={false}
      help={
        <HelperText
          error={error?.message}
          helperText={helperText}
          maxLength={maxLength}
          value={value}
        />
      }
      htmlFor={id}
      label={label}
      required={required}
      validateStatus={error ? 'error' : 'success'}
      {...formItemProps}
    >
      <CountryPhoneInput
        className="customPhoneInput"
        id={id}
        maxLength={maxLength}
        onChange={handleInputChange}
        type="tel"
        value={countryPhoneValue}
        {...fieldRest}
        {...otherProps}
      />
    </Form.Item>
  );
};

export default PhoneInput;
