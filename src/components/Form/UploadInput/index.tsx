import { UploadOutlined } from '@ant-design/icons';
import type { FormItemProps, UploadProps } from 'antd';
import { Button, Form, message, Progress, Upload } from 'antd';
import type { RcFile, UploadFile } from 'antd/es/upload';
import axios from 'axios';
import { useMutate } from 'hooks';
import sharingQuery from 'models/sharing';
import { useEffect, useRef, useState } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

export interface UploadFieldProps<TFormValues extends FieldValues> extends UploadProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: string;
  className?: string;
  formItemProps?: FormItemProps;
  required?: boolean;
  imageOnly?: boolean;
  isDragger?: boolean;
  limitSize?: number;
  fileNameLimit?: number;
}

const { Dragger } = Upload;

const UploadField = <TFormValues extends FieldValues>({
  label,
  className = '',
  control,
  name,
  formItemProps,
  required,
  imageOnly,
  isDragger = false,
  limitSize,
  fileNameLimit,
  ...props
}: UploadFieldProps<TFormValues>) => {
  const { t } = useTranslation();
  const [progress, setProgress] = useState(0);
  const timeoutRef = useRef<number>();

  const { mutateAsync: signUrl, isLoading } = useMutate<
    {
      fileName: string;
      contentType: string;
      isPublic: boolean;
    },
    { key: string; url: string }
  >(sharingQuery.signUrl);

  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        window.clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const handleBeforeUpload = (file: File) => {
    if (imageOnly) {
      const isImageFile = file.type.startsWith('image/');
      if (!isImageFile) {
        message.error('You can only upload Image file!');
        return false || Upload.LIST_IGNORE;
      }
      const isLimitSize = file.size / 1024 / 1024 < 5;
      if (!isLimitSize) {
        message.error('Image must smaller than 5MB!');
        return false || Upload.LIST_IGNORE;
      }
      return true;
    }
    if (fileNameLimit && file.name.length > fileNameLimit) {
      message.error(`Please change the file name less than ${fileNameLimit} chars`);
      return false || Upload.LIST_IGNORE;
    }
    if (limitSize) {
      const isLimitSize = file.size / 1024 / 1024 < limitSize;
      if (!isLimitSize) {
        message.error(`File must smaller than ${limitSize}MB!`);
        return false || Upload.LIST_IGNORE;
      }
    }
    return true;
  };

  const customRequest = ({ file }: { file: string | Blob | RcFile }) => {
    if (file instanceof File) {
      signUrl(
        { fileName: file.name, contentType: file.type, isPublic: true },
        {
          onSuccess: async (data) => {
            const urlObj = new URL(data.url);
            const url = `${urlObj.origin}${urlObj.pathname}`;
            await axios.put(data.url, file, {
              headers: {
                'Content-Type': file.type,
              },
              onUploadProgress: (progressEvent) => {
                if (progressEvent.total) {
                  const percentCompleted = Math.round(
                    (progressEvent.loaded * 100) / progressEvent.total,
                  );
                  setProgress(percentCompleted);
                  if (percentCompleted === 100) {
                    timeoutRef.current = window.setTimeout(() => setProgress(0), 1000);
                  }
                }
              },
            });
            field.onChange([
              { uid: data.key, url, contentType: file.type, name: file.name, status: 'done' },
            ]);
          },
        },
      );
    }
  };

  return (
    <Form.Item
      className={className}
      colon={false}
      help={error?.message}
      label={label}
      required={required}
      validateStatus={error ? 'error' : 'success'}
      {...formItemProps}
    >
      {isDragger ? (
        <Dragger
          beforeUpload={handleBeforeUpload}
          customRequest={customRequest}
          fileList={field.value}
          listType="picture"
          maxCount={3}
          onRemove={(file) =>
            field.onChange(
              ((field.value as UploadFile<any>[]) || []).filter((i) => i.uid !== file.uid),
            )
          }
          {...props}
        >
          <div className="h-[300px] flex flex-col justify-center items-center p-4">
            <p className="ant-upload-text">{t('inquiry:quoteUploadHint')}</p>
            {!!progress && <Progress percent={progress} />}
          </div>
        </Dragger>
      ) : (
        <Upload
          beforeUpload={handleBeforeUpload}
          customRequest={customRequest}
          fileList={field.value}
          listType="picture"
          maxCount={3}
          onRemove={(file) =>
            field.onChange(
              ((field.value as UploadFile<any>[]) || []).filter((i) => i.uid !== file.uid),
            )
          }
          {...props}
        >
          <Button
            className="mb-1"
            color="primary"
            danger={!!error}
            icon={<UploadOutlined />}
            loading={isLoading}
          >
            Upload
          </Button>
        </Upload>
      )}
    </Form.Item>
  );
};

export default UploadField;
