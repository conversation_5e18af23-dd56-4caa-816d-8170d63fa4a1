import type { FormItemProps } from 'antd';
import { Form, Input } from 'antd';
import type { InputProps } from 'antd/lib/input';
import type { ReactNode } from 'react';
import { useId } from 'react';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { useController } from 'react-hook-form';

import HelperText from '../HelperText';

export interface TextInputProps<TFormValues extends FieldValues> extends InputProps {
  name: Path<TFormValues>;
  control: Control<TFormValues>;
  label?: string | ReactNode;
  className?: string;
  inputClassName?: string;
  formItemProps?: FormItemProps;
  required?: boolean;
  helperText?: string;
  showHelper?: boolean;
  onValueChange?: (event: any) => void;
}

const TextInput = <TFormValues extends FieldValues>({
  label,
  className = '',
  control,
  name,
  formItemProps,
  inputClassName,
  required,
  maxLength,
  helperText,
  onValueChange,
  showHelper = true,
  ...props
}: TextInputProps<TFormValues>) => {
  const id = useId();
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });
  const { onChange: onFieldChange, ...fieldRest } = field;
  const { onChange: customOnchange, ...otherProps } = props;
  const handleInputChange = (e: any) => {
    if (onValueChange) {
      onValueChange(e);
    }
    if (customOnchange) {
      customOnchange(e);
    } else {
      onFieldChange(e);
    }
  };
  return (
    <Form.Item
      className={className}
      colon={false}
      help={
        showHelper && (
          <HelperText
            error={error?.message}
            helperText={helperText}
            maxLength={maxLength}
            value={field.value}
          />
        )
      }
      htmlFor={id}
      label={label}
      required={required}
      validateStatus={error ? 'error' : 'success'}
      {...formItemProps}
    >
      <Input
        className={inputClassName}
        id={id}
        maxLength={maxLength}
        {...otherProps}
        {...fieldRest}
        onChange={handleInputChange}
        ref={field.ref}
        value={field.value}
      />
    </Form.Item>
  );
};

export default TextInput;
