import { Col, Row, Space } from 'antd';
import type { ReactNode } from 'react';
import React from 'react';

type Props = {
  className?: string;
  label: ReactNode;
  value: ReactNode;
  icon?: ReactNode;
};

const TherapistFieldValue = ({ label, value, icon, className }: Props) => (
  <Row className={`${className} pb-6 last:pb-0`}>
    <Col sm={8} xs={24}>
      <Space align="center" size={8}>
        {icon}
        <div className="text-[#74788b] pb-1 font-medium">{label}</div>
      </Space>
    </Col>
    <Col className="text-[#080c35]" sm={16} xs={24}>
      {value || '-'}
    </Col>
  </Row>
);

export default TherapistFieldValue;
