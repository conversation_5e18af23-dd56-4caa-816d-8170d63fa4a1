import type { ReactElement } from 'react';
import React from 'react';

type Props = {
  title?: string;
  children: ReactElement | JSX.Element;
  className?: string;
  hideHeader?: boolean;
};

const TherapistLabel = (props: Props) => {
  const { title, children, className = '', hideHeader = false } = props;
  return (
    <div className={`${className} mx-auto bg-white rounded-xl shadow-sm`}>
      {!hideHeader && (
        <>
          <h2 className="px-4 py-4 m-0 md:pl-6 text-lg text-[#080c358d] font-semibold">{title}</h2>
        </>
      )}

      {children}
    </div>
  );
};

export default TherapistLabel;
