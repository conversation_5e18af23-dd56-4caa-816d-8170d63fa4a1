import { Button } from 'antd';

function Error() {
  return (
    <div className="h-screen relative">
      <img alt="" className="w-full h-full object-cover" />
      <div className="top-[20%] absolute translate-x-1/2 right-1/2 w-full p-4">
        <div className="text-[60px] text-[#745700] text-center">404</div>
        <div className="text-[18px] text-[#745700] text-center">
          お探しのページが見つかりませんでした トップから再度お試しください!
        </div>
        <div className="flex justify-center mt-4">
          {/* TODO: If using link here, there is a problem with the loss of css antd prolayout. Using window.location.replace as a workaround */}
          <Button ghost onClick={() => window.location.replace('/')} size="large" type="primary">
            トップに戻る
          </Button>
        </div>
      </div>
    </div>
  );
}

export default Error;
