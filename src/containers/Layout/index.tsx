import {
  CaretDownOutlined,
  LogoutOutlined,
  SettingOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { ProLayout } from '@ant-design/pro-layout';
import { Avatar, Button, Dropdown, Space, Spin, Tooltip } from 'antd';
import type { BreadcrumbItemType } from 'antd/es/breadcrumb/Breadcrumb';
// import LngDropdown from 'components/SwitchLang';
import { useUser } from 'hooks';
import useLogout from 'models/auth/useLogout';
import { Suspense } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, Outlet, useNavigate } from 'react-router-dom';
import { sideMenuRoutes } from 'router';

const PageLayout = () => {
  const { data } = useUser();
  const { logout, context } = useLogout();
  const { t } = useTranslation();
  const navigate = useNavigate();

  return (
    <div id="pro-layout">
      <ProLayout
        ErrorBoundary={false}
        breadcrumbRender={(routers) =>
          (routers || []).map((route, index) => ({
            ...route,
            path: undefined,
            title:
              index < (routers || []).length - 1 ? (
                <Link to={(route as BreadcrumbItemType).path || '/'}>
                  {(route as BreadcrumbItemType).title}
                </Link>
              ) : (
                (route as BreadcrumbItemType).title
              ),
          }))
        }
        colorPrimary="#1890ff"
        formatMessage={(message) => {
          return t(message.defaultMessage || '');
        }}
        headerTitleRender={(logo) => logo}
        layout="mix"
        logo={
          <Link to="/booking">
            <img src="/images/logo.png" />
          </Link>
        }
        menuItemRender={(menuItem, defaultDom) => {
          return (
            <Link key={menuItem.path} to={menuItem.path || '/'}>
              {defaultDom}
            </Link>
          );
        }}
        rightContentRender={() => (
          <div className="flex items-center gap-4">
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'setting',
                    icon: <SettingOutlined />,
                    label: t('profileMenu:setting'),
                    onClick: () => {
                      navigate('/profile/setting');
                    },
                  },
                ],
              }}
            >
              <Button type="text">
                <Space>
                  <Avatar icon={<UserOutlined />} size={24} />
                  <div className="truncate max-w-[18rem] text-left">
                    {data?.name || data?.email}
                  </div>
                  <CaretDownOutlined />
                </Space>
              </Button>
            </Dropdown>

            {/* <LngDropdown /> */}

            <Tooltip placement="bottomRight" title={t('confirmation:logout.title')}>
              <Button icon={<LogoutOutlined />} onClick={logout} shape="circle" />
            </Tooltip>
          </div>
        )}
        route={{ routes: sideMenuRoutes }}
        token={{
          sider: {
            colorTextMenuSelected: '#1890ff',
            colorBgMenuItemSelected: '#e6f7ff',
            colorMenuBackground: '#FFF',
            colorTextMenu: '#000',
          },
          header: {
            colorBgHeader: '#F8FBFD',
          },
          pageContainer: {
            colorBgPageContainer: '#fff',
            paddingInlinePageContainerContent: 16,
          },
        }}
      >
        <Suspense
          fallback={
            <div className="flex justify-center p-4">
              <Spin />
            </div>
          }
        >
          <Outlet />
        </Suspense>
        {context}
      </ProLayout>
    </div>
  );
};

export default PageLayout;
