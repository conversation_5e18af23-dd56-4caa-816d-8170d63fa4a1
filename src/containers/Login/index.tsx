import { yupResolver } from '@hookform/resolvers/yup';
import { Button, Form, Row, Typography } from 'antd';
import PasswordInput from 'components/Form/PasswordInput';
import TextInput from 'components/Form/TextInput';
import HeaderComponent from 'components/Header';
import { useMutate } from 'hooks';
import useUser from 'hooks/useUser';
import authQuery from 'models/auth';
import type { LoginResponse } from 'models/auth/type';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Navigate, useNavigate } from 'react-router';
import Helper from 'utils/helpers';

import type { LoginFormValues } from './schema';
import schema from './schema';

const Login = () => {
  const navigate = useNavigate();

  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm<LoginFormValues>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
  });
  const { t } = useTranslation();
  const { refetch: refetchUser } = useUser({ enabled: false });
  const { mutateAsync: login, isLoading } = useMutate<LoginFormValues, LoginResponse>(
    authQuery.login,
  );

  const handleLogin: SubmitHandler<LoginFormValues> = (values) => {
    login(values, {
      onSuccess: async (data) => {
        Helper.setToken({ ...data }, true);
        refetchUser();
        navigate('/booking', { replace: true });
      },
    });
  };
  const webCookie = Helper.getWebCookie();
  if (webCookie?.token) {
    return <Navigate replace to="/" />;
  }
  return (
    <div className="bg-[#F9F9F9] h-screen">
      <HeaderComponent />
      <div className="p-4">
        <Typography className="font-bold text-3xl text-center mt-20">{t('login:login')}</Typography>
        <div className="p-6 bg-white max-w-md mx-auto rounded-lg mt-8 shadow-lg">
          <Form
            className="flex flex-col gap-1"
            layout="vertical"
            onFinish={handleSubmit(handleLogin)}
          >
            <TextInput
              control={control}
              label={t('login:email')}
              name="email"
              placeholder={t('login:emailPlaceholder')}
            />
            <PasswordInput
              control={control}
              label={t('login:password')}
              name="password"
              placeholder={t('login:passwordPlaceholder')}
              type="password"
            />
            <Row justify="center">
              <a href="/request-forgot-password">パスワードをお忘れですか？</a>
            </Row>
            <Button
              block
              className="mt-2"
              color="primary"
              disabled={!isValid}
              htmlType="submit"
              loading={isLoading}
              size="large"
              type="primary"
            >
              {t('login:login')}
            </Button>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default Login;
