import { t } from 'i18n';
import { REGEX } from 'utils/constants';
import type { InferType } from 'yup';
import { object, string } from 'yup';

const schema = object({
  // <PERSON><PERSON>'s default email regex is wrong for some cases in some projects, so I change the email regex
  // You can read more about this regex here https://colinhacks.com/essays/reasonable-email-regex
  email: string().required().trim().matches(REGEX.EMAIL, t('validation:invalidEmail')).lowercase(),
  password: string().required(),
});

export type LoginFormValues = InferType<typeof schema>;
export default schema;
