import { t } from 'i18n';
import { REGEX } from 'utils/constants';
import type { InferType } from 'yup';
import { object, string } from 'yup';
import * as Yup from 'yup';

const schema = object({
  password: string()
    .trim()
    .required()
    .max(100)
    .matches(REGEX.PASSWORD_POLICY, t('validation:invalidPasswordRule')),
  confirmPassword: string()
    .trim()
    .max(100)
    .required()
    .oneOf([Yup.ref('password')], t('validation:notMatchedPasswords')),
});
export type ResetPassWordFormValues = InferType<typeof schema>;
export default schema;
