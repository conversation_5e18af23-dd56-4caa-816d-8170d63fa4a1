import { yupResolver } from '@hookform/resolvers/yup';
import { Button, Form, Spin, Typography } from 'antd';
import PasswordInput from 'components/Form/PasswordInput';
import HeaderComponent from 'components/Header';
import { useMutate } from 'hooks';
import useHSBToken from 'hooks/useHSBToken';
import salonQuery from 'models/salons';
import type { IResetPassword } from 'models/salons/type';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import Helper from 'utils/helpers';

import type { ResetPassWordFormValues } from './schema';
import schema from './schema';

const { Text } = Typography;
const ResetPasswordPage = () => {
  const navigate = useNavigate();

  const { t } = useTranslation();

  const {
    token,
    isLoading: isLoadingCheckToken,
    isValidToken,
    responsePayload,
  } = useHSBToken(salonQuery.checkTokenExpired.apiUrl, salonQuery?.checkTokenExpired.method);

  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm<ResetPassWordFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
  });

  const { mutateAsync: forgotPassword, isLoading } = useMutate<IResetPassword>(
    salonQuery.resetPassword,
  );
  const handleForgotPassword: SubmitHandler<ResetPassWordFormValues> = (values) => {
    forgotPassword(
      {
        password: values?.password,
        verifyToken: token,
      },
      {
        onSuccess: async () => {
          setTimeout(() => Helper.removeWebCookie());
          navigate('/login');
        },
      },
    );
  };
  if (isLoadingCheckToken || !isValidToken) {
    return (
      <div
        id="big-spin"
        style={{
          position: 'fixed',
          top: '10%',
          left: 0,
          width: '100vw',
          height: '100vh',
          display: 'flex',
          justifyContent: 'center',
          zIndex: 10000,
        }}
      >
        <Spin size="large" />
      </div>
    );
  }
  return (
    <div className="h-screen">
      <HeaderComponent />
      <div className="mt-20">
        <Typography className="font-bold text-3xl text-center mt-4">
          {t('forgotPassword:forgotPasswordTitle')}
        </Typography>
        <div className="p-6 bg-white max-w-md mx-auto rounded-lg mt-8 ring-1">
          <Form
            className="flex flex-col gap-1"
            layout="vertical"
            onFinish={handleSubmit(handleForgotPassword)}
          >
            <Form.Item colon={false} label={t('common:email')}>
              <Text>{responsePayload?.email}</Text>
            </Form.Item>
            <PasswordInput
              control={control}
              label={t('forgotPassword:newPassword')}
              name="password"
              placeholder={t('forgotPassword:newPasswordPlaceholder')}
              required
              type="password"
            />
            <PasswordInput
              control={control}
              label={t('forgotPassword:confirmNewPassword')}
              name="confirmPassword"
              placeholder={t('forgotPassword:confirmNewPasswordPlaceHoler')}
              required
              type="password"
            />
            <Button
              block
              className="mt-2"
              color="primary"
              disabled={!isValid}
              htmlType="submit"
              loading={isLoading}
              size="large"
              type="primary"
            >
              {t('global:save')}
            </Button>
          </Form>
        </div>
      </div>
    </div>
  );
};
export default ResetPasswordPage;
