import { yupResolver } from '@hookform/resolvers/yup';
import { Button, Form, Typography } from 'antd';
import TextInput from 'components/Form/TextInput';
import HeaderComponent from 'components/Header';
// import HeaderComponent from 'components/Header';
import { useMutate } from 'hooks';
import salonQuery from 'models/salons';
import type { IForgotPasswordRequest } from 'models/salons/type';
import React from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';

import type { RequestForgotPasswordFormValues } from './schema';
import schema from './schema';

const RequestForgotPassword = () => {
  const navigate = useNavigate();
  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm<RequestForgotPasswordFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
  });
  const { t } = useTranslation();
  const { mutateAsync: handleRequireRequestPassword, isLoading } =
    useMutate<IForgotPasswordRequest>(salonQuery.requestForgotPassword);
  const handleRequireForgotPassword: SubmitHandler<RequestForgotPasswordFormValues> = (values) => {
    handleRequireRequestPassword(values, {
      onSuccess: async () => {
        navigate('/sent-email-success');
      },
    });
  };
  return (
    <div className="h-screen">
      <HeaderComponent />
      <div className="mt-20">
        <Typography className="font-bold text-3xl text-center mt-4">
          {t('forgotPassword:forgotPasswordTitle')}
        </Typography>
        <div className="p-6 bg-white max-w-md mx-auto rounded-lg mt-8 ring-1">
          <div className=" text-center mt-4 mb-8">{t('forgotPassword:forgotPasswordContent')}</div>
          <Form
            className="flex flex-col gap-1"
            layout="vertical"
            onFinish={handleSubmit(handleRequireForgotPassword)}
          >
            <TextInput
              control={control}
              label={t('login:email')}
              name="email"
              placeholder={t('login:emailPlaceholder')}
              required
            />
            <Button
              block
              className="mt-2"
              color="primary"
              disabled={!isValid}
              htmlType="submit"
              loading={isLoading}
              size="large"
              type="primary"
            >
              {t('forgotPassword:sendEmail')}
            </Button>
          </Form>
        </div>
      </div>
    </div>
  );
};
export default RequestForgotPassword;
