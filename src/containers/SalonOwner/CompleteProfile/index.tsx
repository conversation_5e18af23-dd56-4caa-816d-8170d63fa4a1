import { yupResolver } from '@hookform/resolvers/yup';
import { Button, Form, Spin, Typography } from 'antd';
import PasswordInput from 'components/Form/PasswordInput';
import type { LoginFormValues } from 'containers/Login/schema';
import { useMutate } from 'hooks';
import useHSBToken from 'hooks/useHSBToken';
import authQuery from 'models/auth';
import type { LoginResponse } from 'models/auth/type';
import salonQuery from 'models/salons';
import type { IActiveSalon } from 'models/salons/type';
import React from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import Helper from 'utils/helpers';

import type { ActiveAccountFormValues } from './schema';
import schema from './schema';

const { Text } = Typography;

const SalonCompleteProfile = () => {
  const navigate = useNavigate();

  const { t } = useTranslation();
  const { token, responsePayload, isLoading, isValidToken } = useHSBToken();

  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm<ActiveAccountFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
  });
  const { mutateAsync: activeSalonAccount } = useMutate<IActiveSalon>(salonQuery.activeSalon);
  const { mutateAsync: login } = useMutate<LoginFormValues, LoginResponse>(authQuery.login);
  const handleForgotPassword: SubmitHandler<ActiveAccountFormValues> = (values) => {
    activeSalonAccount(
      {
        password: values?.password,
        verifyToken: token,
      },
      {
        onSuccess: async () => {
          login(
            {
              email: responsePayload?.email,
              password: values?.password,
            },
            {
              onSuccess: async (data) => {
                Helper.setToken({ ...data }, true);
                navigate('/booking', { replace: true });
              },
            },
          );
        },
      },
    );
  };
  if (isLoading || !isValidToken) {
    return (
      <div
        id="big-spin"
        style={{
          position: 'fixed',
          top: '10%',
          left: 0,
          width: '100vw',
          height: '100vh',
          display: 'flex',
          justifyContent: 'center',
          zIndex: 10000,
        }}
      >
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="h-screen">
      <div className="pt-32">
        <Typography className="font-bold text-3xl text-center mt-4">
          {t('registerAccount:title')}
        </Typography>
        <div className="p-6 bg-white max-w-lg min-w-[600px] mx-auto rounded-lg mt-8 ring-1">
          <Form
            className="flex flex-col gap-1"
            layout="vertical"
            onFinish={handleSubmit(handleForgotPassword)}
          >
            <PasswordInput
              control={control}
              label={t('login:password')}
              name="password"
              placeholder={t('login:passwordPlaceholder')}
              required
              type="password"
            />
            <PasswordInput
              control={control}
              label={t('registerAccount:passwordConfirm')}
              name="confirmPassword"
              placeholder={t('registerAccount:passwordConfirmPlaceholder')}
              required
              type="password"
            />
            <Text className="link">
              利用を開始することで、{' '}
              <a
                href="https://info.hogugu.com/information/terms-of-use/index.html"
                rel="noreferrer"
                style={{ textDecoration: 'underline', textUnderlineOffset: 1.5 }}
                target="_blank"
              >
                利用規約
              </a>{' '}
              /{' '}
              <a
                className="text-decoration"
                href="https://info.hogugu.com/information/privacy.html"
                rel="noreferrer"
                style={{ textDecoration: 'underline', textUnderlineOffset: 1.5 }}
                target="_blank"
              >
                プライバシーポリシー
              </a>{' '}
              に同意したとみなします。
            </Text>
            <Button
              block
              className="mt-4"
              color="primary"
              disabled={!isValid}
              htmlType="submit"
              size="large"
              type="primary"
            >
              {t('global:register')}
            </Button>
          </Form>
        </div>
      </div>
    </div>
  );
};
export default SalonCompleteProfile;
