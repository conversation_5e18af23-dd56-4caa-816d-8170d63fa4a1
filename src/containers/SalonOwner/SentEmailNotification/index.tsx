import { Button } from 'antd';
import HeaderComponent from 'components/Header';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

const SentEmailNotificationPage = () => {
  const { t } = useTranslation();
  return (
    <div className="h-screen">
      <HeaderComponent />
      <div className="pt-20 flex items-center justify-center">
        <div className="flex items-center justify-center">
          <div className="w-[560px] flex flex-col items-center p-8 relative  bg-[#E8F1FB] shadow-md rounded-2xl">
            <div className="text-[24px] mb-12 font-bold  text-center ">
              パスワード再設定メール <br />
              送信完了
            </div>
            <div className="text-center pl-8 pr-8">
              {t('forgotPassword:sentEmailInforDescription')}
            </div>
            <div className="mt-8 flex items-center justify-center">
              <Link to={'/login'}>
                <Button ghost style={{ width: 160 }} type="primary">
                  ログインする
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default SentEmailNotificationPage;
