import { yupResolver } from '@hookform/resolvers/yup';
import { Button, Col, Form, notification, Row, Space, Typography } from 'antd';
import PasswordInput from 'components/Form/PasswordInput';
import { useMutate } from 'hooks';
import useLogout from 'models/auth/useLogout';
import salonQuery from 'models/salons';
import { useForm } from 'react-hook-form';

import type { ResetPassWordFormValues } from './schema';
import schema from './schema';

type Props = {
  onCancel: () => void;
};

const ResetPasswordForm = ({ onCancel }: Props) => {
  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm<ResetPassWordFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
  });
  const { logoutWithoutConfirm } = useLogout();
  const { mutate: changePassword, isLoading } = useMutate<{
    oldPassword: string;
    password: string;
  }>({ ...salonQuery.changePassword });

  const onSubmitForm = async (values: ResetPassWordFormValues) => {
    changePassword(
      {
        oldPassword: values.currentPassword,
        password: values.newPassword,
      },
      {
        onSuccess: () => {
          notification.success({
            message: 'パスワードを変更しました',
          });
          onCancel();
          setTimeout(logoutWithoutConfirm, 1000);
        },
      },
    );
  };

  return (
    <div className="min-w-[360px] md:min-w-[680px] mx-auto p-4">
      <Form className="flex flex-col gap-1" layout="vertical" onFinish={handleSubmit(onSubmitForm)}>
        <Row>
          <Col span={8}>
            <Space>
              <div>現在のパスワード</div>
              <Typography.Text type="danger">*</Typography.Text>
            </Space>
          </Col>
          <Col span={16}>
            <PasswordInput
              control={control}
              inputClassname="rounded-md"
              name="currentPassword"
              placeholder={'現在のパスワードを入力してください'}
              required
              type="password"
            />
          </Col>
        </Row>
        <Row>
          <Col span={8}>
            <Space>
              <div>新しいパスワード</div>
              <Typography.Text type="danger">*</Typography.Text>
            </Space>
          </Col>
          <Col span={16}>
            <PasswordInput
              control={control}
              inputClassname="rounded-md"
              name="newPassword"
              placeholder={'新しいパスワードを入力してください'}
              required
              type="password"
            />
          </Col>
        </Row>
        <Row>
          <Col span={8}>
            <Space>
              <div>新しいパスワード（確認）</div>
              <Typography.Text type="danger">*</Typography.Text>
            </Space>
          </Col>
          <Col span={16}>
            <PasswordInput
              control={control}
              inputClassname="rounded-md"
              name="confirmNewPassword"
              placeholder={'新しいパスワード（確認）を入力してください'}
              required
              type="password"
            />
          </Col>
        </Row>

        <div className="flex justify-end gap-x-2">
          <Button
            className="w-full max-w-xs lg:w-28"
            onClick={onCancel}
            size="large"
            type="default"
          >
            キャンセル
          </Button>
          <Button
            className="w-full max-w-xs lg:w-28 "
            disabled={!isValid}
            htmlType="submit"
            loading={isLoading}
            size="large"
            type="primary"
          >
            保存
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default ResetPasswordForm;
