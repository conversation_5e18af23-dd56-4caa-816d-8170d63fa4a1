import { REGEX } from 'utils/constants';
import type { InferType } from 'yup';
import { object, string } from 'yup';

const schema = object({
  currentPassword: string()
    .trim()
    .required('パスワードを入力してください')
    .max(100)
    .matches(
      REGEX.PASSWORD_POLICY,
      'パスワードは8文字以上、半角英数字、英字大小文字を使用してください。',
    ),
  newPassword: string()
    .trim()
    .required('パスワードを入力してください')
    .max(100)
    .matches(
      REGEX.PASSWORD_POLICY,
      'パスワードは8文字以上、半角英数字、英字大小文字を使用してください。',
    )
    .test({
      name: 'is-new-password',
      test(value, ctx) {
        if (value === ctx.parent.currentPassword) {
          return ctx.createError({
            message: '現在のパスワードとは別のパスワードを入力してください',
          });
        }

        return true;
      },
    }),
  confirmNewPassword: string()
    .trim()
    .max(100)
    .test({
      name: 'is-confirm-new-password',
      test(value, ctx) {
        if (!value || value.length === 0) {
          return ctx.createError({ message: 'パスワードを入力してください' });
        }
        if (!REGEX.PASSWORD_POLICY.test(value)) {
          return ctx.createError({
            message: 'パスワードは8文字以上、半角英数字、英字大小文字を使用してください。',
          });
        }
        if (value === ctx.parent.currentPassword) {
          return ctx.createError({
            message: '現在のパスワードとは別のパスワードを入力してください',
          });
        }
        if (value !== ctx.parent.newPassword) {
          return ctx.createError({ message: 'パスワードが一致しません。' });
        }
        return true;
      },
    }),
});
export type ResetPassWordFormValues = InferType<typeof schema>;
export default schema;
