import type { ModalFuncProps } from 'antd';
import { Modal } from 'antd';
import React, { useEffect } from 'react';

import ResetPasswordForm from '../ResetPasswordForm';

const useResetPasswordModal = () => {
  const [modal, context] = Modal.useModal();
  const { destroyAll } = Modal;

  useEffect(() => {
    return () => destroyAll();
  }, [destroyAll]);

  const resetPasswordModal = (options?: ModalFuncProps) => {
    const customModal = modal.confirm({
      icon: null,
      centered: true,
      className: 'min-w-[360px] md:min-w-[760px]',
      maskClosable: false,
      title: 'パスワード変更',
      footer: false,
      ...options,
    });
    customModal.update({
      content: <ResetPasswordForm onCancel={customModal.destroy} />,
    });

    return customModal;
  };
  return { context, resetPasswordModal };
};

export default useResetPasswordModal;
