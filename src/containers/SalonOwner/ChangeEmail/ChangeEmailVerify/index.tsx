import BigSpin from 'components/BigSpin';
import { useMutate } from 'hooks';
import { useEffectOnce } from 'hooks/useEffectOnce';
import salonQuery from 'models/salons';
import { useLocation, useNavigate } from 'react-router';
import Helper from 'utils/helpers';

const ChangeEmailVerify = () => {
  const navigate = useNavigate();
  const { search } = useLocation();
  const searchParams = new URLSearchParams(search);
  const token = searchParams.get('token') as string;
  const { mutate: changeEmailWithToken } = useMutate<{ verifyToken: string }>({
    ...salonQuery.changeEmailWithToken,
    onSuccess: async () => {
      setTimeout(() => Helper.removeWebCookie());
      navigate('/login');
    },
    onError: () => {
      navigate('/url-expired', { replace: true });
    },
  });

  useEffectOnce(() => {
    if (token) {
      changeEmailWithToken({
        verifyToken: token,
      });
    }
  });

  return <BigSpin />;
};

export default ChangeEmailVerify;
