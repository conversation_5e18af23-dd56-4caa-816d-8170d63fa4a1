import { REGEX } from 'utils/constants';
import type { InferType } from 'yup';
import { object, string } from 'yup';

const schema = object({
  currentEmail: string().trim(),
  newEmail: string()
    .trim()
    .required('メールアドレスを入力してください')
    .max(100, 'この項目は入力必須、100文字以内です')
    .test({
      name: 'is-new-email',
      test(value, ctx) {
        if (!value || value.length === 0) {
          return ctx.createError({ message: 'メールアドレスを入力してください' });
        }
        if (!REGEX.EMAIL.test(value)) {
          return ctx.createError({ message: 'メールフォーマットが無効です' });
        }
        if (value === ctx.parent.currentEmail) {
          return ctx.createError({
            message: '現在のメールアドレスとは別のメールアドレスを入力してください',
          });
        }
        return true;
      },
    }),
  confirmNewEmail: string()
    .max(100, 'この項目は入力必須、100文字以内です')
    .test({
      name: 'is-confirm-email',
      test(value, ctx) {
        if (!value || value.length === 0) {
          return ctx.createError({ message: 'メールアドレスを入力してください' });
        }
        if (!REGEX.EMAIL.test(value)) {
          return ctx.createError({ message: 'メールフォーマットが無効です' });
        }
        if (value === ctx.parent.currentEmail) {
          return ctx.createError({
            message: '現在のメールアドレスとは別のメールアドレスを入力してください',
          });
        }
        if (value !== ctx.parent.newEmail) {
          return ctx.createError({ message: 'メールアドレスが一致しません' });
        }
        return true;
      },
    }),
});

export type ChangeSLOwnerPasswordInputFields = InferType<typeof schema>;
export default schema;
