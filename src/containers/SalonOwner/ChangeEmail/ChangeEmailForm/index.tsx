import { yupResolver } from '@hookform/resolvers/yup';
import { Button, Col, Form, Input, notification, Row, Space, Typography } from 'antd';
import TextInput from 'components/Form/TextInput';
import { useMutate } from 'hooks';
import salonQuery from 'models/salons';
import { useForm } from 'react-hook-form';

import type { ChangeSLOwnerPasswordInputFields } from './schema';
import schema from './schema';

type Props = {
  currentEmail: string;
  onCancel: () => void;
};

const ChangeEmailForm = ({ onCancel, currentEmail }: Props) => {
  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = useForm<ChangeSLOwnerPasswordInputFields>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
    defaultValues: { currentEmail },
  });
  const { mutateAsync: requestToChangeEmail, isLoading } = useMutate<{ email: string }>({
    ...salonQuery.requestToChangeEmail,
  });

  const onSubmitForm = async (values: ChangeSLOwnerPasswordInputFields) => {
    requestToChangeEmail(
      {
        email: values.newEmail,
      },
      {
        onSuccess: () => {
          notification.success({
            message: '承認メールを送信しました',
          });
          setTimeout(onCancel, 300);
        },
      },
    );
  };

  return (
    <div className="min-w-[360px] md:min-w-[700px] mx-auto pr-4 mt-2">
      <Form onFinish={handleSubmit(onSubmitForm)}>
        <Row>
          <Col span={8}>
            <Space>
              <div>現在のメールアドレス</div>
              <Typography.Text type="danger">*</Typography.Text>
            </Space>
          </Col>
          <Col span={16}>
            <Input
              className="rounded-md mb-6 cursor-not-allowed"
              disabled
              name="currentEmail"
              readOnly
              required
              value={currentEmail}
            />
          </Col>
        </Row>

        <Row>
          <Col span={8}>
            <Space>
              <div>新しいメールアドレス</div>
              <Typography.Text type="danger">*</Typography.Text>
            </Space>
          </Col>
          <Col span={16}>
            <TextInput
              control={control}
              inputClassName="rounded-md"
              name="newEmail"
              placeholder="新しいメールアドレスを入力してください"
              required
            />
          </Col>
        </Row>

        <Row>
          <Col span={8}>
            <Space>
              <div>新しいメールアドレス（確認）</div>
              <Typography.Text type="danger">*</Typography.Text>
            </Space>
          </Col>
          <Col span={16}>
            <TextInput
              control={control}
              inputClassName="rounded-md"
              name="confirmNewEmail"
              placeholder="もう一度新しいメールアドレスを入力してください"
              required
            />
          </Col>
        </Row>

        <div className="flex justify-end gap-x-2">
          <Button
            className="w-full max-w-xs lg:w-28"
            onClick={onCancel}
            size="large"
            type="default"
          >
            キャンセル
          </Button>
          <Button
            className="w-full max-w-xs lg:w-28 "
            disabled={!isValid}
            htmlType="submit"
            loading={isLoading}
            size="large"
            type="primary"
          >
            保存
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default ChangeEmailForm;
