import type { ModalFuncProps } from 'antd';
import { Modal } from 'antd';
import { useEffect } from 'react';

import ChangeEmailForm from '../ChangeEmailForm';

type Props = {
  currentEmail: string;
};
const useChangeEmailModal = ({ currentEmail }: Props) => {
  const [modal, context] = Modal.useModal();
  const { destroyAll } = Modal;

  useEffect(() => {
    return () => destroyAll();
  }, [destroyAll]);

  const changeEmailModal = (options?: ModalFuncProps) => {
    const customModal = modal.confirm({
      icon: null,
      centered: true,
      className: 'min-w-[360px] md:min-w-[760px]',
      maskClosable: false,
      title: 'メールアドレス変更',
      footer: false,
      ...options,
    });
    customModal.update({
      content: <ChangeEmailForm currentEmail={currentEmail} onCancel={customModal.destroy} />,
    });

    return customModal;
  };
  return { context, changeEmailModal };
};

export default useChangeEmailModal;
