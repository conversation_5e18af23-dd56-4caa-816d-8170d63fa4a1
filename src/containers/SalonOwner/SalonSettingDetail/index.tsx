import { PageContainer } from '@ant-design/pro-layout';
import { useTranslation } from 'react-i18next';
import { Outlet, useLocation, useNavigate } from 'react-router';

const SalonSettingDetail = () => {
  const { t } = useTranslation();

  const navigate = useNavigate();
  const { pathname } = useLocation();
  const tabList = [
    {
      key: 'setting',
      tab: t('salonSetting:profileTab'),
    },
    {
      key: 'bank-info',
      tab: t('salonSetting:bankInfoTab'),
    },
  ];

  const handleTabChange = (key: string) => {
    navigate(`/profile/${key}`, { replace: true });
  };

  return (
    <PageContainer
      header={{
        title: t('profileMenu:setting'),
      }}
      onTabChange={handleTabChange}
      tabActiveKey={pathname.split(`/`).pop()}
      tabList={tabList}
    >
      <Outlet />
    </PageContainer>
  );
};

export default SalonSettingDetail;
