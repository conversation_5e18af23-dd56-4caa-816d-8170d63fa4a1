import { EditOutlined } from '@ant-design/icons';
import { Button, Form, Space } from 'antd';
import BigSpin from 'components/BigSpin';
import useChangeEmailModal from 'containers/SalonOwner/ChangeEmail/hooks/useChangeEmailModal';
import useResetPasswordModal from 'containers/SalonOwner/ResetPassword/hooks/useResetPasswordModal';
import { useUser } from 'hooks';
import { useTranslation } from 'react-i18next';

import ProfileForm from './ProfileForm';

const BasicInfo = () => {
  const { t } = useTranslation();

  const { data: salonInfo, isLoading } = useUser();

  const { context: resetPasswordContext, resetPasswordModal } = useResetPasswordModal();
  const { context: changeEmailContext, changeEmailModal } = useChangeEmailModal({
    currentEmail: salonInfo?.email || '',
  });

  if (isLoading) {
    return <BigSpin />;
  }

  return (
    <>
      <div className="profile-basic-info">
        <div className="mt-4">
          <div className="max-w-2xl">
            <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
              <Form.Item label={<p className="font-bold">{t('common:email')}</p>}>
                <Space className="flex justify-between" size={'middle'}>
                  <div> {salonInfo?.email}</div>
                  <Button
                    icon={<EditOutlined />}
                    onClick={() => changeEmailModal()}
                    size="middle"
                    type="primary"
                  >
                    {t('global:change')}
                  </Button>
                </Space>
              </Form.Item>
              <Form.Item label={<p className="font-bold">{t('login:password')}</p>}>
                <Space className="flex justify-between" size="large">
                  <div> *********</div>
                  <Button
                    icon={<EditOutlined />}
                    onClick={() => resetPasswordModal()}
                    size="middle"
                    type="primary"
                  >
                    {t('global:change')}
                  </Button>
                </Space>
              </Form.Item>
              <Form.Item label={<p className="font-bold">{t('salonSetting:salonID')}</p>}>
                {salonInfo?._id}
              </Form.Item>
            </Form>
          </div>
          <ProfileForm />
        </div>
      </div>
      {resetPasswordContext}
      {changeEmailContext}
    </>
  );
};

export default BasicInfo;
