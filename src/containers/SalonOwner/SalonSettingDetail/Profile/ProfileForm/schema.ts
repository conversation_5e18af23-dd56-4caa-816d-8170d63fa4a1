import { t } from 'i18n';
import type { InferType } from 'yup';
import { object, string } from 'yup';

const schema = object({
  name: string().required(t('validation:requiredSalonName')).trim().max(100),
  phone: string().trim().nullable(),

  postalCode: string()
    .trim()
    .test({
      name: 'postalCode',
      exclusive: false,
      params: {},
      message: '７桁の番号を入力してください',
      test(value) {
        if (value && value?.length < 7) {
          return false;
        }
        return true;
      },
    })
    .test({
      name: 'postalCode',
      exclusive: false,
      params: {},
      message: t('validation:requiredPostCode'),
      test(value, context) {
        if (
          !value &&
          (context.parent.city ||
            context.parent.prefecture ||
            context.parent.address ||
            context.parent.building)
        ) {
          return false;
        }

        return true;
      },
    })

    .nullable(),
  prefecture: string()
    .nullable()
    .test({
      name: 'prefecture',
      exclusive: false,
      params: {},
      message: t('validation:requiredPrefecture'),
      test(value, context) {
        if (
          !value &&
          (context.parent.postalCode ||
            context.parent.city ||
            context.parent.address ||
            context.parent.building)
        ) {
          return false;
        }

        return true;
      },
    })
    .nullable(),
  city: string()
    .trim()
    .max(20)
    .test({
      name: 'city',
      exclusive: false,
      params: {},
      message: t('validation:requiredCityWard'),
      test(value, context) {
        if (
          !value &&
          (context.parent.postalCode ||
            context.parent.prefecture ||
            context.parent.address ||
            context.parent.building)
        ) {
          return false;
        }

        return true;
      },
    })
    .nullable(),

  address: string()
    .trim()
    .max(10)
    .test({
      name: 'address',
      exclusive: false,
      params: {},
      message: t('validation:requiredStreetBlock'),
      test(value, context) {
        if (
          !value &&
          (context.parent.postalCode ||
            context.parent.prefecture ||
            context.parent.city ||
            context.parent.building)
        ) {
          return false;
        }

        return true;
      },
    })
    .nullable(),

  building: string().trim().max(50).nullable(),
  note: string().trim().max(500).nullable(),
});

export type ProfileFields = InferType<typeof schema>;
export default schema;
