/* eslint-disable react/jsx-sort-props */
import { yupResolver } from '@hookform/resolvers/yup';
import { Button, Form, notification } from 'antd';
import SelectInput from 'components/Form/SelectInput';
import TextAreaInput from 'components/Form/TextAreaInput';
import TextInput from 'components/Form/TextInput';
import { useMutate, useUser } from 'hooks';
import useControlList from 'hooks/useControlList';
import salonQuery from 'models/salons';
import sharingQuery from 'models/sharing';
import type { IPrefecture } from 'models/sharing/type';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import queryClient from 'utils/queryClient';

import type { ProfileFields } from './schema';
import schema from './schema';

export type ProfileFormsProps = {
  prefectureList?: { _id: string; name: string }[];
};
const ProfileForm = () => {
  const { t } = useTranslation();
  const { data: salonInfo, refetch: refetchUser } = useUser();
  const {
    control,
    handleSubmit,
    setValue,
    formState: { isValid },
  } = useForm<Omit<ProfileFields, 'prefecture'> & { prefecture: number | null }>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
    values: {
      name: salonInfo?.name || '',
      phone: salonInfo?.phone,
      postalCode: salonInfo?.postalCode,
      city: salonInfo?.city,
      address: salonInfo?.address,
      building: salonInfo?.building,
      note: salonInfo?.note,
      prefecture: salonInfo?.prefecture?.iso ? Number(salonInfo?.prefecture?.iso) : null,
    },
  });
  const { mutateAsync: updateSalon, isLoading: isUpdating } = useMutate<
    Omit<ProfileFields, 'phone' | 'prefecture'> & {
      phone: string | null;
      prefecture: { iso: string | number; name: string } | null;
    }
  >(salonQuery.updateProfile);

  const { list: prefectureList } = useControlList<IPrefecture>(sharingQuery.prefectures);
  const handlePostalCodeToAddress = async (postalCode: string) => {
    if (postalCode.length === 7) {
      try {
        const addressData = await queryClient.ensureQueryData(
          sharingQuery.postalCodeToAddress(postalCode),
        );
        setValue('prefecture', addressData.address.prefecture._id);
        setValue('city', addressData.address.city.name.toString(), { shouldValidate: true });
        setValue('address', addressData.address.ward.name.toString(), { shouldValidate: true });
      } catch (error: any) {
        // not found postal code
        if (error?.code === 404) {
          notification.error({
            message: '郵便番号が無効です',
            key: error?.code,
          });
        }
      }
    }
  };
  const handleUpdate: SubmitHandler<
    Omit<ProfileFields, 'prefecture'> & { prefecture: number | null }
  > = (values) => {
    const prefectureItem = (prefectureList || []).find((p) => p._id === Number(values?.prefecture));
    const payload = {
      name: values?.name,
      postalCode: values?.postalCode || null,
      city: values?.city || null,
      address: values?.address || null,
      building: values?.building || null,
      note: values?.note || null,
      prefecture: prefectureItem
        ? {
            iso: prefectureItem._id,
            name: prefectureItem.name,
          }
        : null,
      phone: values?.phone ? values?.phone : null,
    };
    updateSalon(payload, {
      onSuccess: () => {
        refetchUser();
        notification.success({
          message: '入力した内容を保存しました' || t('validation:completed'),
        });
      },
    });
  };

  return (
    <div className="max-w-2xl">
      <Form
        className="flex flex-col gap-1"
        id="basic-infor-form"
        labelCol={{ span: 8 }}
        layout="horizontal"
        onFinish={handleSubmit(handleUpdate)}
        wrapperCol={{ span: 16 }}
      >
        <TextInput
          allowClear
          control={control}
          label={t('salonSetting:salonName')}
          maxLength={100}
          name="name"
          placeholder="ABCサロン"
          required
        />
        <TextInput
          allowClear
          control={control}
          label={t('common:phone')}
          maxLength={20}
          name="phone"
          placeholder="00 0000 0000"
        />

        <TextInput
          allowClear
          control={control}
          label={
            <p>
              {t('salonSetting:postCode')}
              <div style={{ fontSize: 11 }}>✳︎ハイフンなし</div>
            </p>
          }
          maxLength={7}
          onValueChange={async (e) => {
            if (e) {
              const event = e?.target as HTMLInputElement;
              handlePostalCodeToAddress(event?.value);
            }
          }}
          placeholder="1234567"
          name="postalCode"
          // Only Input number 0-9
          onKeyPress={(e: React.KeyboardEvent<HTMLInputElement>) => {
            const target = e.target as HTMLInputElement;
            const inputValue = target?.value;
            const key = e.which || e.key;
            if (inputValue?.length < 7) {
              if (Number(key) < 48 || Number(key) > 57) {
                e.preventDefault();
              }
            } else e.preventDefault();
          }}
        />

        <SelectInput
          allowClear
          control={control}
          data={prefectureList}
          label={t('salonSetting:prefecture')}
          name="prefecture"
          placeholder={t('global:pleaseSelect')}
          showSearch
        />
        <TextInput
          allowClear
          control={control}
          label={t('salonSetting:city')}
          maxLength={20}
          name="city"
          placeholder=" 港区六本木"
        />
        <TextInput
          allowClear
          control={control}
          label={t('salonSetting:townVillage')}
          maxLength={10}
          name="address"
          placeholder="1-2-3"
        />
        <TextInput
          allowClear
          control={control}
          label={t('salonSetting:buildingName')}
          maxLength={50}
          name="building"
          placeholder="AZビール 1階 123"
        />
        <TextAreaInput
          allowClear
          control={control}
          label={t('salonSetting:remarks')}
          maxLength={500}
          name="note"
          placeholder="自由に入力してください"
          rows={4}
        />
        <div className="flex justify-center">
          <Button
            className="w-full max-w-[14rem]"
            disabled={!isValid}
            htmlType="submit"
            key={'update'}
            loading={isUpdating}
            size="large"
            type="primary"
          >
            {t('global:save')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default ProfileForm;
