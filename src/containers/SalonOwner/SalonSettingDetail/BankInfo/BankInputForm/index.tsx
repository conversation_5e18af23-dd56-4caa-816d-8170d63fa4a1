/* eslint-disable react/jsx-sort-props */
/* eslint-disable react-hooks/exhaustive-deps */
import { yupResolver } from '@hookform/resolvers/yup';
import { Button, Form, notification } from 'antd';
import BigSpin from 'components/BigSpin';
import SelectInput from 'components/Form/SelectInput';
import TextInput from 'components/Form/TextInput';
import { useFetch, useMutate, useUser } from 'hooks';
import salonQuery from 'models/salons';
import type {
  IBankAPIRes,
  IBankBranchAPIRes,
  IBankBranchItem,
  IBankSalonInfo,
} from 'models/salons/type';
import { useEffect } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useForm, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { BANK_ACCOUNT_TYPE } from 'utils/constants';

import type { BankFields } from './schema';
import schema from './schema';

const BankInputForm = () => {
  const { t } = useTranslation();
  const { data: salonInfo } = useUser();

  const { data: salonBankInfo, refetch: refetchSalonBankInfo } = useFetch<IBankSalonInfo>(
    salonQuery.getSalonBankInfo,
  );
  const {
    control,
    clearErrors,
    handleSubmit,
    setValue,
    formState: { isValid },
  } = useForm<BankFields>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
    values: {
      bankId: salonBankInfo?.bankId || '',
      bankName: salonBankInfo?.bankName || '',
      bankCode: salonBankInfo?.bankCode || '',
      branchId: salonBankInfo?.branchId || '',
      branchName: salonBankInfo?.branchName || '',
      branchCode: salonBankInfo?.branchCode || '',
      name: salonBankInfo?.name || '',
      cardType: salonBankInfo?.cardType || '',
      cardNumber: salonBankInfo?.cardNumber || '',
    },
  });

  const { data: bankList = [], isLoading: loadingBankList } = useFetch<
    IBankAPIRes,
    { _id: string; name: string; code: string }[]
  >({
    ...salonQuery.getBankList,
    select: (data) =>
      data.docs
        ? data.docs.map((i) => ({
            _id: i._id,
            name: i.bankName,
            code: i.bankCode,
          }))
        : [],
  });
  // branch list -> bank Id
  const bankValue = useWatch({ name: 'bankId', control });
  useEffect(() => {
    const bankItem = (bankList || []).find((b) => b._id === bankValue);
    if (bankItem) {
      setValue('bankName', bankItem?.name);
      setValue('bankCode', bankItem?.code);
      setValue('bankId', bankItem?._id);
    }
  }, [bankValue]);
  const { data: branchList, isLoading: isLoadingBranchList } = useFetch<
    IBankBranchAPIRes,
    { _id: string; name: string; code: string }[]
  >({
    ...salonQuery.getBankBranchList(bankValue || (salonBankInfo?.bankId as string)),
    select: (data) =>
      data.docs
        ? data.docs.map((b: IBankBranchItem) => ({
            _id: b._id,
            name: b.branchName,
            code: b.branchCode,
          }))
        : [],
  });
  const branchValue = useWatch({ name: 'branchId', control });
  const bankBranchItem = (branchList || []).find(
    (bl) => bl._id === (branchValue || salonBankInfo?.branchId),
  );

  const { mutateAsync: updateSalon, isLoading: isUpdating } = useMutate<
    BankFields | 'userid',
    'branchId' | 'bankId'
  >(salonQuery.updateBankInfo);

  const clearFieldWithBankName = () => {
    clearErrors('branchId');
    clearErrors('branchName');
    clearErrors('branchCode');
    setValue('branchId', '');
    setValue('branchCode', '');
    setValue('branchName', '');
  };

  // auto set value for branch code
  useEffect(() => {
    if (bankBranchItem) {
      setValue('branchId', bankBranchItem?._id);
      setValue('branchCode', bankBranchItem?.code);
      setValue('branchName', bankBranchItem?.name);
    }
  }, [branchValue]);
  const handleUpdate: SubmitHandler<BankFields> = (values) => {
    const bankItem = (bankList || []).find((b) => b._id === values?.bankId);
    const branchItem = (branchList || []).find((bl) => bl._id === values?.branchId);
    const payload = {
      branchCode: branchItem?.code || salonBankInfo?.branchCode || '',
      branchName: branchItem?.name || salonBankInfo?.branchName || '',
      branchId: values?.branchId || '',
      name: values?.name || '',
      cardType: values?.cardType || '',
      cardNumber: values?.cardNumber || '',
      bankCode: bankItem?.code || salonBankInfo?.bankCode || '',
      bankName: bankItem?.name || salonBankInfo?.bankName || '',
      bankId: values?.bankId || '',
      userId: salonInfo?._id || '',
      // branchCode: branchItem?.branchId || salonBankInfo?.branchCode || '',
    };
    updateSalon(payload, {
      onSuccess: () => {
        refetchSalonBankInfo();
        notification.success({
          message: '銀行口座情報を保存しました' || t('validation:completed'),
        });
      },
    });
  };

  if (loadingBankList) {
    return <BigSpin />;
  }

  return (
    <div className="max-w-2xl">
      <Form
        className="flex flex-col gap-1"
        id="basic-infor-form"
        labelCol={{ span: 8 }}
        layout="horizontal"
        onFinish={handleSubmit(handleUpdate)}
        wrapperCol={{ span: 16 }}
      >
        <SelectInput
          allowClear
          showSearch
          control={control}
          data={bankList}
          label="金融機関名"
          name="bankId"
          onClear={() => clearFieldWithBankName()}
          onSelect={() => clearFieldWithBankName()}
          placeholder={t('global:pleaseSelect')}
          required
        />
        <SelectInput
          allowClear
          showSearch
          control={control}
          data={branchList}
          label="支店名"
          loading={isLoadingBranchList}
          name="branchId"
          onClear={() => setValue('branchId', '')}
          placeholder={t('global:pleaseSelect')}
          required
        />
        <TextInput
          control={control}
          label="支店コード"
          name="branchCode"
          placeholder="支店を選択してください"
          readOnly
          required
          value={bankBranchItem?.code}
        />
        <SelectInput
          allowClear
          control={control}
          data={BANK_ACCOUNT_TYPE}
          label="口座種別"
          name="cardType"
          placeholder={t('global:pleaseSelect')}
          required
        />

        <TextInput
          allowClear
          control={control}
          label="口座番号"
          maxLength={7}
          placeholder="1234567"
          required
          name="cardNumber"
          // Only Input number 0-9
          onKeyPress={(e: React.KeyboardEvent<HTMLInputElement>) => {
            const target = e.target as HTMLInputElement;
            const inputValue = target?.value;
            const key = e.which || e.key;
            if (inputValue?.length < 7) {
              if (Number(key) < 48 || Number(key) > 57) {
                e.preventDefault();
              }
            } else e.preventDefault();
          }}
        />
        <TextInput
          allowClear
          control={control}
          label="口座名義"
          maxLength={100}
          name="name"
          placeholder="ヤマダタロウ"
          required
        />

        <div className="flex justify-center">
          <Button
            className="w-full max-w-[14rem]"
            disabled={!isValid}
            htmlType="submit"
            key={'update'}
            loading={isUpdating}
            size="large"
            type="primary"
          >
            {t('global:save')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default BankInputForm;
