import { t } from 'i18n';
import { REGEX } from 'utils/constants';
import type { InferType } from 'yup';
import { object, string } from 'yup';

const schema = object({
  bankId: string().trim().required(t('validation:requiredBankName')),
  bankCode: string().trim().required(t('validation:requiredBankName')),
  bankName: string().trim().required(t('validation:requiredBankName')),
  branchId: string().trim().required(t('validation:requiredBranchName')),
  branchName: string().trim().required(t('validation:requiredBranchName')),
  branchCode: string(),
  name: string()
    .trim()
    .required(t('validation:requiredAccountHolderName'))
    .matches(REGEX.KATAKANA, t('validation:invalidAccountHolderName')),
  cardType: string().trim().required(t('validation:requiredAccountType')),
  cardNumber: string()
    .trim()
    .required(t('validation:requiredAccountNumber'))
    .test({
      name: 'cardNumber',
      exclusive: false,
      params: {},
      message: t('validation:maxLengthAccountNumber'),
      test(value) {
        if (value && value?.length < 7) {
          return false;
        }
        return true;
      },
    }),
});

export type BankFields = InferType<typeof schema>;
export default schema;
