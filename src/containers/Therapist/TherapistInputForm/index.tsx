import { yupResolver } from '@hookform/resolvers/yup';
import { Button, Col, Form, Row, Space, Typography } from 'antd';
import PhoneInput from 'components/Form/PhoneInput';
import TextInput from 'components/Form/TextInput';
import { useForm } from 'react-hook-form';

import type { TherapistInputFields } from './schema';
import schema from './schema';

export type TherapistInputFormsProps = {
  onSubmit: (values: TherapistInputFields) => void;
  onCancel?: () => void | undefined;
  loading?: boolean;
  isSuccess?: boolean;
};

const TherapistInputForms = ({ onSubmit, onCancel, loading }: TherapistInputFormsProps) => {
  const {
    control,
    handleSubmit,
    formState: { isValid },
    setValue,
  } = useForm<TherapistInputFields>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
  });
  return (
    <div className="min-w-[360px] md:min-w-[640px] mx-auto p-4">
      <Form onFinish={handleSubmit(onSubmit)}>
        <Row>
          <Col span={7}>
            <Space>
              <div>登録名：</div>
              <Typography.Text type="danger">*</Typography.Text>
            </Space>
          </Col>
          <Col span={17}>
            <TextInput
              control={control}
              inputClassName="rounded-md"
              name="nickName"
              placeholder="ヤマダ"
              required
            />
          </Col>
        </Row>

        <Row>
          <Col span={7}>
            <Space>
              <div>お名前：</div>
              <Typography.Text type="danger">*</Typography.Text>
            </Space>
          </Col>
          <Col span={17}>
            <TextInput
              control={control}
              inputClassName="rounded-md"
              name="fullName"
              placeholder="山田太郎"
              required
            />
          </Col>
        </Row>

        <Row>
          <Col span={7}>
            <Space>
              <div>
                <div className="text-base">電話番号 :</div>
                <div className="text-[10px] text-opacity-50">✳︎ハイフンなし</div>
              </div>
              <Typography.Text type="danger">*</Typography.Text>
            </Space>
          </Col>
          <Col span={17}>
            <PhoneInput
              control={control}
              inline
              name="phone"
              onChange={(values) => {
                setValue('phone', values.phone || '');
                setValue('phoneCode', `${values.code}`);
              }}
              placeholder="00 0000 0000"
              required
            />
          </Col>
        </Row>

        <Row>
          <Col span={7}>
            <Space>
              <div>メールアドレス：</div>
              <Typography.Text type="danger">*</Typography.Text>
            </Space>
          </Col>
          <Col span={17}>
            <TextInput
              control={control}
              inputClassName="rounded-md"
              name="email"
              placeholder="<EMAIL>"
              required
            />
          </Col>
        </Row>

        <div className="flex justify-end gap-x-2">
          <Button
            className="w-full max-w-xs lg:w-28"
            onClick={onCancel}
            size="large"
            type="default"
          >
            キャンセル
          </Button>
          <Button
            className="w-full max-w-xs lg:w-28 "
            disabled={!isValid}
            htmlType="submit"
            loading={loading}
            size="large"
            type="primary"
          >
            作成
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default TherapistInputForms;
