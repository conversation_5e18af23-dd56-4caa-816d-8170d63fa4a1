import { REGEX } from 'utils/constants';
import type { InferType } from 'yup';
import { object, string } from 'yup';

const schema = object({
  nickName: string()
    .trim()
    .required('登録名を入力してください')
    .max(50, 'この項目は入力必須、50文字以内です。'),
  fullName: string()
    .trim()
    .required('お名前を入力してください')
    .max(50, 'この項目は入力必須、50文字以内です。'),
  phone: string()
    .required('9〜12の数字から入力してください')
    .matches(REGEX.PHONE, '9〜12の数字から入力してください'),
  phoneCode: string(),
  email: string()
    .trim()
    .required('メールアドレスを入力してください')
    .max(100, 'この項目は入力必須、100文字以内です。')
    .matches(REGEX.EMAIL, 'メールフォーマットが無効です。'),
});

export type TherapistInputFields = InferType<typeof schema>;
export default schema;
