import { CloseCircleFilled, SearchOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns } from '@ant-design/pro-table';
import { But<PERSON>, Col, Row } from 'antd';
import CustomTable from 'components/CustomTable';
import TextInput from 'components/Form/TextInput';
import SearchWrapper from 'components/SearchWrapper';
import useList from 'hooks/useList';
import therapistQuery from 'models/therapist';
import type { ITherapist } from 'models/therapist/types';
import { useLocation, useNavigate } from 'react-router';

import useCreateTherapistModal from '../Create/hooks/useCreateTherapistModal';
import { columns } from './column';
import schema from './searchSchema';

const TherapistList = () => {
  const { pathname } = useLocation();
  const navigate = useNavigate();

  const { list, isFetching, pagination } = useList<ITherapist>(therapistQuery.list);
  const { context, createTherapistModal } = useCreateTherapistModal();
  const TherapistColumn: ProColumns<ITherapist>[] = [
    columns._id,
    columns.fullName,
    columns.nickName,
    columns.gender,
    columns.email,
    columns.phone,
    columns.setting,
  ];
  return (
    <>
      <PageContainer style={{ minHeight: '100vh' }}>
        <Row className="w-full">
          <Col span={12}>
            <SearchWrapper layout="vertical" schema={schema}>
              {({ control, reset, setValue }, setQuery) => (
                <Row className="max-w-md">
                  <Col span={20}>
                    <TextInput
                      allowClear={{
                        clearIcon: (
                          <CloseCircleFilled
                            onClick={() => {
                              reset();
                              setQuery({
                                page: 1,
                                keyword: '',
                              });
                            }}
                          />
                        ),
                      }}
                      control={control}
                      inputClassName="rounded-s-md rounded-none"
                      maxLength={100}
                      name="keyword"
                      onChange={(e) => {
                        const { value } = e.target;
                        setValue('keyword', value);
                        if (value === '') {
                          setQuery({
                            page: 1,
                            keyword: '',
                          });
                        }
                      }}
                      placeholder={'ID,名前、登録名、メールアドレス、電話番号'}
                      showHelper={false}
                      size="large"
                    />
                  </Col>
                  <Col span={4}>
                    <Button
                      className="!rounded-e-md !rounded-s-none"
                      form="search-form"
                      htmlType="submit"
                      icon={<SearchOutlined />}
                      size="large"
                      type="primary"
                    >
                      検索
                    </Button>
                  </Col>
                </Row>
              )}
            </SearchWrapper>
          </Col>
          <Col className="text-right lg:my-auto" span={12}>
            <Button
              className="w-full max-w-xs lg:w-28 "
              htmlType="submit"
              onClick={() => createTherapistModal()}
              size="large"
              type="primary"
            >
              新規追加
            </Button>
          </Col>
        </Row>
        <CustomTable
          columns={TherapistColumn}
          dataSource={list}
          loading={isFetching}
          locale={{
            emptyText: () => (
              <div className="text-lg py-32">該当するセラピストが見つかりませんでした。</div>
            ),
          }}
          onRow={(record) => {
            return {
              onClick: () => {
                navigate(`${pathname}/${record?._id}/profile-info`);
              },
            };
          }}
          pagination={pagination}
          rowClassName="hover:cursor-pointer"
        />
      </PageContainer>
      {context}
    </>
  );
};

export default TherapistList;
