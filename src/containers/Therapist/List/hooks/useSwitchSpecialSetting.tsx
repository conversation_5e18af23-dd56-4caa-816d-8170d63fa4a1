import { useQueryClient } from '@tanstack/react-query';
import type { ModalFuncProps } from 'antd';
import { Button, Divider, Modal, notification } from 'antd';
import { useMutate } from 'hooks';
import therapistQuery from 'models/therapist';
import type { ITherapist } from 'models/therapist/types';
import { useEffect } from 'react';

type Props = {
  therapist: ITherapist;
};
const ModalContent = ({ therapist, onCancel }: { therapist: ITherapist; onCancel: () => void }) => {
  const queryClient = useQueryClient();

  const { mutateAsync: swtich, isLoading } = useMutate<{ status: boolean }>(
    therapistQuery.switchSpecialSetting(therapist._id),
  );

  const isOn = therapist?.specialTreatmentTime?.status || false;

  return (
    <>
      <Divider className="m-0" />
      <div className="py-6 px-4 text-sm text-[#080c35] ">
        {isOn
          ? 'Are you want to turn off'
          : 'オンにした場合、最低施術時間が90分以上の予約のみを受け付けます。'}
      </div>
      <Divider className="m-0" />
      <div className="flex justify-center md:justify-end gap-x-2 px-4 md:px-6 py-4">
        <Button className="w-full max-w-xs lg:w-28" onClick={onCancel} size="large" type="default">
          キャンセル
        </Button>
        <Button
          className="w-[151px] md:w-[94px] h-[32px] !rounded-lg"
          loading={isLoading}
          onClick={() => {
            swtich(
              { status: !isOn },
              {
                onSuccess: () => {
                  notification.success({ message: '特別設定を設定しました' });
                  queryClient.invalidateQueries(therapistQuery.list);
                  setTimeout(onCancel, 500);
                },
              },
            );
          }}
          size="large"
          type="primary"
        >
          はい
        </Button>
      </div>
    </>
  );
};
const useSwitchSpecialSettingModal = ({ therapist }: Props) => {
  const [modal, context] = Modal.useModal();
  const { destroyAll } = Modal;

  useEffect(() => {
    return () => destroyAll();
  }, [destroyAll]);

  const switchSpecialSettingModal = (options?: ModalFuncProps) => {
    const customModal = modal.confirm({
      icon: null,
      centered: true,
      className: 'min-w-[360px] md:min-w-[580px]',
      maskClosable: false,
      footer: false,
      title: '特別設定',
      ...options,
    });
    customModal.update({
      content: <ModalContent onCancel={customModal.destroy} therapist={therapist} />,
    });

    return customModal;
  };
  return { context, switchSpecialSettingModal };
};

export default useSwitchSpecialSettingModal;
