import type { ProColumns } from '@ant-design/pro-table';
import { Typography } from 'antd';
import type { ITherapist } from 'models/therapist/types';
import { GenderFormat } from 'utils/constants';
import Helper from 'utils/helpers';

import SpecailSettingComponent from './SpecailSettingComponent';

const { Paragraph } = Typography;

type TherapistColumns = Pick<
  ITherapist,
  '_id' | 'fullName' | 'nickName' | 'gender' | 'email' | 'setting' | 'phone'
>;
export const columns: Record<keyof TherapistColumns, ProColumns> = {
  _id: {
    dataIndex: '_id',
    width: 180,
    title: 'ID',
    copyable: true,
  },
  fullName: {
    dataIndex: 'fullName',
    title: '名前',
    ellipsis: true,
  },
  nickName: {
    dataIndex: 'nickName',
    title: '登録名',
    ellipsis: true,
  },
  gender: {
    dataIndex: 'gender',
    title: '性別',
    render: (_, data: ITherapist) => (
      <Paragraph style={{ marginBottom: 0 }}>
        {data?.gender || data?.gender === 0 ? GenderFormat[data?.gender] : '-'}
      </Paragraph>
    ),
  },
  email: {
    dataIndex: 'email',
    title: 'メールアドレス',
  },
  phone: {
    dataIndex: 'phone',
    title: '電話番号',
    render: (_, data: ITherapist) => (
      <Paragraph style={{ marginBottom: 0 }}>
        {Helper.formatPhoneNumber(`${data?.phone}`)}
      </Paragraph>
    ),
  },
  setting: {
    dataIndex: 'setting',
    title: '特別設定',
    render: (_, data: ITherapist) => <SpecailSettingComponent therapist={data} />,
  },
};
