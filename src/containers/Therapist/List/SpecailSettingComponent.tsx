import { useQueryClient } from '@tanstack/react-query';
import { notification, Switch } from 'antd';
import { useMutate } from 'hooks';
import therapistQuery from 'models/therapist';
import type { ITherapist } from 'models/therapist/types';
import React from 'react';

import useSwitchSpecialSettingModal from './hooks/useSwitchSpecialSetting';

type Props = {
  therapist: ITherapist;
};

const SpecialSettingComponent = ({ therapist }: Props) => {
  const { switchSpecialSettingModal, context } = useSwitchSpecialSettingModal({ therapist });
  const queryClient = useQueryClient();

  const { mutateAsync: swtich } = useMutate<{ status: boolean }>(
    therapistQuery.switchSpecialSetting(therapist._id),
  );

  const onSpecialSettingChange = () => {
    const isOn = therapist?.specialTreatmentTime?.status || false;
    if (isOn) {
      swtich(
        { status: !isOn },
        {
          onSuccess: () => {
            notification.success({ message: '特別設定を設定しました' });
            queryClient.invalidateQueries(therapistQuery.list);
          },
        },
      );
    } else {
      switchSpecialSettingModal();
    }
  };

  return (
    <div onClick={(e) => e.stopPropagation()}>
      <Switch
        checked={therapist?.specialTreatmentTime?.status || false}
        onChange={onSpecialSettingChange}
      />
      {context}
    </div>
  );
};

export default SpecialSettingComponent;
