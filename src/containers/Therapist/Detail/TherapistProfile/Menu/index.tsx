import { Avatar, Divider, Empty, List, Spin } from 'antd';
import { useFetch } from 'hooks';
import { ArrowDownLineIcon, ArrowUpLineIcon } from 'icons';
import _values from 'lodash/values';
import therapistQuery from 'models/therapist';
import type { ITherapistMenu } from 'models/therapist/types';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import formatCurrencyJPY from 'utils/number';

type Props = {
  data: ITherapistMenu;
  isShowAllMenu: boolean;
};

const MenuItem = ({ data, isShowAllMenu }: Props) => {
  const [isShowMenu, setIsShowMenu] = useState(false);

  useEffect(() => {
    if (isShowAllMenu !== isShowMenu) {
      setIsShowMenu(isShowAllMenu);
    }
  }, [isShowAllMenu]);

  return (
    <List.Item className="!p-4 !mb-4 bg-[#E4EBEF] !border-0 rounded-lg" key={data._id}>
      <List.Item.Meta
        avatar={
          <Avatar
            icon={<Empty />}
            size={32}
            src={data.images && data.images.icon && data.images.icon.url}
            style={{ backgroundColor: 'transparent' }}
          />
        }
        className="!mb-0"
        style={{ alignItems: 'center' }}
        title={
          <div className="text-sm text-[#324452] flex justify-between">
            {data.title}
            <div
              className="flex items-center cursor-pointer"
              onClick={() => setIsShowMenu((prevIsShowMenu) => !prevIsShowMenu)}
            >
              {isShowMenu ? <ArrowUpLineIcon /> : <ArrowDownLineIcon />}
            </div>
          </div>
        }
      />
      {isShowMenu && (
        <div className="flex mt-3">
          <div style={{ width: 'calc(50% - 1px)' }}>
            <ul className="pl-[30px] w-fit">
              {_values(data.options)
                .slice(0, 3)
                .map((item, index) => (
                  <li key={data._id + item.duration} style={{ marginTop: index > 0 ? 6 : 0 }}>
                    <div className="text-[#324452] flex justify-between">
                      <span className="mr-2 w-[65px]">{item.duration} 分</span>
                      <span>{formatCurrencyJPY(item.price)}</span>
                    </div>
                  </li>
                ))}
            </ul>
          </div>
          <div style={{ borderLeft: '1px solid #A7B5C0', margin: '0 16px' }} />
          <div style={{ width: 'calc(50% - 1px)' }}>
            <ul className="pl-[30px] w-fit">
              {_values(data.options)
                .slice(3)
                .map((item, index) => (
                  <li key={data._id + item.duration} style={{ marginTop: index > 0 ? 6 : 0 }}>
                    <div className="text-[#324452] flex justify-between">
                      <span className="mr-2 w-[65px]">
                        {item.duration === 10 && `延長 `}
                        {item.duration} 分
                      </span>
                      <span>{formatCurrencyJPY(item.price)}</span>
                    </div>
                  </li>
                ))}
            </ul>
          </div>
        </div>
      )}
    </List.Item>
  );
};

const Menu = () => {
  const { id = '' } = useParams();
  const { data: menuData, isLoading } = useFetch<ITherapistMenu[]>(
    therapistQuery.getTherapistMenu(id as string),
  );
  const [isShowAllMenu, setIsShowAllMenu] = useState(false);

  return (
    <div>
      <div className="mx-auto bg-white rounded-xl shadow-sm">
        <h2 className="px-4 py-4 m-0 md:pl-6 text-lg text-[#080c358d] font-semibold flex justify-between">
          対応可能メニュー
          <div
            className="flex items-center gap-1 text-sm font-medium text-[#1890FF] cursor-pointer"
            onClick={() => setIsShowAllMenu((prevIsShowAllMenu) => !prevIsShowAllMenu)}
          >
            {isShowAllMenu ? (
              <>
                閉じる
                <ArrowUpLineIcon />
              </>
            ) : (
              <>
                開く
                <ArrowDownLineIcon />
              </>
            )}
          </div>
        </h2>
        <Divider className="m-0 bg-[#d9dbe9] " />
        <div className="p-6">
          {isLoading ? (
            <Spin />
          ) : (
            <>
              {menuData?.length === 0 ? (
                '対応可能メニューがありません'
              ) : (
                <div>
                  <List
                    dataSource={menuData}
                    itemLayout="vertical"
                    renderItem={(data) => <MenuItem data={data} isShowAllMenu={isShowAllMenu} />}
                    size="small"
                  />
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Menu;
