import { LockFilled, UnlockFilled } from '@ant-design/icons';
import { Button, Divider, notification, Space, Tag } from 'antd';
import TherapistFieldValue from 'components/Therapist/Detail/Profile/TherapistFieldValue';
import TherapistLabel from 'components/Therapist/Detail/Profile/TherapistLabel';
import dayjs from 'dayjs';
import { useMutate } from 'hooks';
import {
  HGBriefcaseIcon,
  HGDatetimeIcon,
  HGDeparturpointIcon,
  HGEmailIcon,
  HGGenderIcon,
  HGIntroduceIcon,
  HGLocationIcon,
  HGPhoneIcon,
  HGTherapistIcon,
  HGUserCardIcon,
} from 'icons';
import therapistQuery from 'models/therapist';
import type { AreaCode, ITherapist, TherapistStatus } from 'models/therapist/types';
import { GenderFormat } from 'utils/constants';
import Helper from 'utils/helpers';
import queryClient from 'utils/queryClient';

import useBlockTherapistModal from '../hooks/useBlockTherapistModal';

type Props = {
  therapist: ITherapist;
};
const ProfileInfo = ({ therapist }: Props) => {
  const { context, blockTherapistModal } = useBlockTherapistModal({ therapist });
  const { mutateAsync: switchStatus, isLoading } = useMutate<{ status: TherapistStatus }>(
    therapistQuery.blockTherapist(therapist?._id),
  );
  const isProfileLock = therapist?.status === 'LOCKED';
  const listArea: { area: AreaCode[]; midnight: AreaCode[]; departurePoint: AreaCode[] } = {
    area: [],
    midnight: [],
    departurePoint: [],
  };

  const handleLockAndUnLockProfile = () => {
    if (!isProfileLock) {
      blockTherapistModal();
    } else {
      switchStatus(
        { status: 'ACTIVE' },
        {
          onSuccess: () => {
            notification.success({ message: 'ブロックを解除しました' });
            queryClient.invalidateQueries(therapistQuery.profileDetail(therapist._id));
          },
        },
      );
    }
  };

  const getAreaCodes = (areaCodes: AreaCode[] | undefined, limit: number): AreaCode[] => {
    let area: AreaCode[] = [];
    if (!areaCodes) return area;
    for (let i = 0; i < areaCodes.length; i += 1) {
      const areaTemp = areaCodes[i];
      if (areaTemp)
        if (areaTemp.children) {
          area = area.concat(getAreaCodes(areaTemp.children, limit));
        } else {
          area.push(areaTemp);
        }
    }
    return area;
  };

  const showAreaCodes = (
    areaCodes: AreaCode[],
    limit: number,
    key: 'area' | 'midnight' | 'departurePoint',
  ): AreaCode[] => {
    listArea[key] = getAreaCodes(areaCodes, limit);
    return listArea[key].slice(0, limit).map((area: AreaCode) => area);
  };

  return (
    <div>
      <TherapistLabel title="基本情報">
        <>
          <Divider className="m-0 bg-[#d9dbe9] " />
          <div className="p-6">
            {/* Name */}
            <TherapistFieldValue
              icon={<HGTherapistIcon />}
              label={'セラピスト名'}
              value={therapist?.fullName}
            />
            {/* Gender */}
            <TherapistFieldValue
              icon={<HGGenderIcon />}
              label="性別"
              value={
                (therapist?.gender || therapist?.gender === 0) && GenderFormat[therapist.gender]
              }
            />
            {/* Date of birth */}
            <TherapistFieldValue
              icon={<HGDatetimeIcon />}
              label="生年月日"
              value={therapist?.birthday && dayjs(therapist?.birthday).format('YYYY年MM月DD日')}
            />
            {/* Phone number */}
            <TherapistFieldValue
              icon={<HGPhoneIcon />}
              label="電話番号"
              value={therapist?.phone && Helper.formatPhoneNumber(therapist?.phone)}
            />
            {/* Email Address */}
            <TherapistFieldValue
              icon={<HGEmailIcon />}
              label="メールアドレス"
              value={therapist?.email}
            />
            {/* Introduction */}
            <TherapistFieldValue
              icon={<HGIntroduceIcon />}
              label={
                <div>
                  自己紹介 <br />
                  (一言メッセージ)
                </div>
              }
              value={therapist?.introduction}
            />
            {/* Experience */}
            <TherapistFieldValue
              icon={<HGBriefcaseIcon />}
              label="マッサージ歴"
              value={therapist?.experience?.name}
            />
            {/* Certificate */}
            <TherapistFieldValue
              icon={<HGUserCardIcon />}
              label={
                <div>
                  自己PR <br />
                  (保有資格や経歴など)
                </div>
              }
              value={therapist?.certificate}
            />
          </div>
          <Divider className="m-0 bg-[#d9dbe9] " />
          <div className="p-6">
            {/* Cover area_label */}
            <TherapistFieldValue
              icon={<HGLocationIcon />}
              label="対応可能エリア"
              value={
                therapist?.areaCodes &&
                therapist?.areaCodes.length > 0 && (
                  <Space size={[0, 8]} wrap>
                    {showAreaCodes(therapist?.areaCodes, 50, 'area').map((area) => (
                      <Tag className="text-[black] bg-[#E4EBEF]" key={area.areaCode}>
                        {area.name}
                      </Tag>
                    ))}
                  </Space>
                )
              }
            />
            {/* Night cover area_label */}
            <TherapistFieldValue
              icon={<HGLocationIcon />}
              label="深夜対応可能エリア"
              value={
                therapist?.midnightSetting &&
                therapist?.midnightSetting?.areas &&
                therapist?.midnightSetting?.areas.length > 0 && (
                  <Space size={[0, 8]} wrap>
                    {showAreaCodes(therapist?.midnightSetting?.areas, 50, 'midnight').map(
                      (area) => (
                        <Tag className="text-[black] bg-[#E4EBEF]" key={area.areaCode}>
                          {area.name}
                        </Tag>
                      ),
                    )}
                  </Space>
                )
              }
            />
            {/* Departure point_label */}
            <TherapistFieldValue
              icon={<HGDeparturpointIcon />}
              label="出発地"
              value={
                therapist?.departurePoint &&
                therapist?.departurePoint?.length > 0 && (
                  <Space size={[0, 8]} wrap>
                    {showAreaCodes(therapist?.departurePoint, 50, 'departurePoint').map(
                      (area, index) => (
                        <span className="text-[black] " key={area.areaCode}>
                          {index !== 0 && <span className="mr-1">,</span>}
                          {area.name}
                        </span>
                      ),
                    )}
                  </Space>
                )
              }
            />
          </div>
        </>
      </TherapistLabel>
      <Button
        className={`text-[red] !border-[red] hover:!text-[red]
        }w-full max-w-md lg:w-fit  mt-2  !rounded-sm`}
        icon={isProfileLock ? <UnlockFilled /> : <LockFilled />}
        loading={isLoading}
        onClick={handleLockAndUnLockProfile}
        size="large"
        type="default"
      >
        {isProfileLock ? 'ブロックを解除' : 'ブロック'}
      </Button>
      {context}
    </div>
  );
};

export default ProfileInfo;
