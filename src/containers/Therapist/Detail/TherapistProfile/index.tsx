import { Col, Row } from 'antd';
import BigSpin from 'components/BigSpin';
import { useFetch } from 'hooks';
import therapistQuery from 'models/therapist';
import type { ITherapist } from 'models/therapist/types';
import { useParams } from 'react-router';

import ActivitiesLog from './ActivitiesLog';
import Menu from './Menu';
import ProfileInfo from './ProfileInfo';
import Summary from './Summary';

const TherapistProfile = () => {
  const { id = '' } = useParams();
  const { data: therapistProfile, isLoading } = useFetch<ITherapist>(
    therapistQuery.profileDetail(id as string),
  );
  if (isLoading) {
    return <BigSpin />;
  }
  return (
    <div>
      <Row gutter={[24, 24]}>
        <Col {...{ md: 12, sm: 24 }}>
          {therapistProfile && <ProfileInfo therapist={therapistProfile} />}
        </Col>
        <Col {...{ md: 12, sm: 24 }}>
          <Row gutter={[24, 12]}>
            <Col span={24}>
              <Summary summaryRate={therapistProfile?.summaryResponseRate} />
            </Col>
            <Col span={24}>
              <Menu />
            </Col>
            <Col span={24}>
              <ActivitiesLog
                createdAt={therapistProfile?.createdAt}
                scheduleLastUpdateTime={therapistProfile?.scheduleLastUpdateTime}
              />
            </Col>
          </Row>
        </Col>
      </Row>
    </div>
  );
};

export default TherapistProfile;
