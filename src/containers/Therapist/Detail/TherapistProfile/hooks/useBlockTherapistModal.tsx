import { useQueryClient } from '@tanstack/react-query';
import type { ModalFuncProps } from 'antd';
import { Button, Divider, Modal, notification } from 'antd';
import { useMutate } from 'hooks';
import therapistQuery from 'models/therapist';
import type { ITherapist, TherapistStatus } from 'models/therapist/types';
import { useEffect } from 'react';

type Props = {
  therapist: ITherapist;
};
const ModalContent = ({ therapist, onCancel }: { therapist: ITherapist; onCancel: () => void }) => {
  const queryClient = useQueryClient();

  const { mutateAsync: switchStatus, isLoading } = useMutate<{ status: TherapistStatus }>(
    therapistQuery.blockTherapist(therapist._id),
  );

  return (
    <>
      <Divider className="m-0" />
      <div className="py-6 px-4 text-sm text-[#080c35] ">
        このセラピストをブロックしてもよろしいでしょうか?ブロックされたユーザーはサービスを利用できなくなります。
      </div>
      <Divider className="m-0" />
      <div className="flex justify-center md:justify-end gap-x-2 px-4 md:px-6 py-4">
        <Button className="w-full max-w-xs lg:w-28" onClick={onCancel} size="large" type="default">
          キャンセル
        </Button>
        <Button
          className="w-[151px] md:w-[94px] h-[32px] !rounded-lg"
          loading={isLoading}
          onClick={() => {
            switchStatus(
              { status: 'LOCKED' },
              {
                onSuccess: () => {
                  notification.success({ message: 'このセラピストをブロックしました' });
                  queryClient.invalidateQueries(therapistQuery.profileDetail(therapist._id));
                },
                onSettled: () => {
                  setTimeout(onCancel, 500);
                },
              },
            );
          }}
          size="large"
          type="primary"
        >
          はい
        </Button>
      </div>
    </>
  );
};
const useBlockTherapistModal = ({ therapist }: Props) => {
  const [modal, context] = Modal.useModal();
  const { destroyAll } = Modal;

  useEffect(() => {
    return () => destroyAll();
  }, [destroyAll]);

  const blockTherapistModal = (options?: ModalFuncProps) => {
    const customModal = modal.confirm({
      icon: null,
      centered: true,
      className: 'min-w-[360px] md:min-w-[580px]',
      maskClosable: false,
      footer: false,
      title: 'ブロックの確認',
      ...options,
    });
    customModal.update({
      content: <ModalContent onCancel={customModal.destroy} therapist={therapist} />,
    });

    return customModal;
  };
  return { context, blockTherapistModal };
};

export default useBlockTherapistModal;
