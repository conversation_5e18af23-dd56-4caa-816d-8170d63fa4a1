import { Divider } from 'antd';
import TherapistFieldValue from 'components/Therapist/Detail/Profile/TherapistFieldValue';
import TherapistLabel from 'components/Therapist/Detail/Profile/TherapistLabel';
import { HGClockIcon } from 'icons';
import Helper from 'utils/helpers';

type Props = {
  scheduleLastUpdateTime: string | undefined;
  createdAt: string | undefined;
};
const ActivitiesLog = ({ createdAt, scheduleLastUpdateTime }: Props) => {
  return (
    <TherapistLabel title="稼働状況">
      <div>
        <Divider className="m-0 bg-[#d9dbe9] " />
        <div className="p-6">
          {/* Last shift update time_label */}
          <TherapistFieldValue
            icon={<HGClockIcon />}
            label={'最終シフト更新日時'}
            value={
              scheduleLastUpdateTime &&
              Helper.formatDayJST(scheduleLastUpdateTime, 'YYYY-MM-DD HH:mm')
            }
          />
          {/* Created at_label */}
          <TherapistFieldValue
            icon={<HGClockIcon />}
            label={'アカウント作成日'}
            value={createdAt && Helper.formatDayJST(createdAt, 'YYYY-MM-DD HH:mm')}
          />
        </div>
      </div>
    </TherapistLabel>
  );
};

export default ActivitiesLog;
