import { Col, Divider, <PERSON>, <PERSON> } from 'antd';
import TherapistLabel from 'components/Therapist/Detail/Profile/TherapistLabel';
import { HGCheckIcon, HGClockIcon, HGRateResponseIcon } from 'icons';
import type { SummaryResponseRate } from 'models/therapist/types';
import Helper from 'utils/helpers';

type Props = {
  summaryRate: SummaryResponseRate | undefined;
};
const Summary = ({ summaryRate }: Props) => {
  return (
    <TherapistLabel hideHeader>
      <div className="p-6">
        <Row>
          <Col className="text-center" span={7}>
            {/* Approval rate_label */}
            <Space direction="vertical">
              <Space size={4}>
                <HGCheckIcon /> <div className="pb-1">承認率</div>
              </Space>
              <div>{summaryRate?.approvalRate ? `${summaryRate?.approvalRate}%` : '100%'}</div>
            </Space>
          </Col>
          <Divider className="h-12" type="vertical" />
          <Col className="text-center " span={7}>
            {/* Response rate_label */}
            <Space direction="vertical">
              <Space size={4}>
                <HGRateResponseIcon /> <div className="pb-1">返答率</div>
              </Space>
              <div>{summaryRate?.responseRate ? `${summaryRate?.responseRate}%` : '100%'}</div>
            </Space>
          </Col>
          <Divider className="h-12" type="vertical" />
          <Col className="text-center" span={7}>
            {/* Response time rate_label */}
            <Space direction="vertical">
              <Space size={4}>
                <HGClockIcon /> <div className="pb-1">返答時間</div>
              </Space>
              <div>{Helper.convertToJapaneseTime(summaryRate?.responseTime)}</div>
            </Space>
          </Col>
        </Row>
      </div>
    </TherapistLabel>
  );
};

export default Summary;
