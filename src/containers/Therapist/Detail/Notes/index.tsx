/* eslint-disable react-hooks/exhaustive-deps */
import { yupResolver } from '@hookform/resolvers/yup';
import { Button, Card, Form, notification, Space } from 'antd';
import TextAreaInput from 'components/Form/TextAreaInput';
import { useFetch, useMutate } from 'hooks';
import { t } from 'i18next';
import therapistQuery from 'models/therapist';
import type { INoteInfo } from 'models/therapist/types';
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import { useParams } from 'react-router';

import type { NoteInputField } from './schema';
import schema from './schema';

const NotesForm = () => {
  const { id = '' } = useParams();
  const userRole = 'therapist';
  const { data: noteInfo, refetch: refetchNote } = useFetch<INoteInfo>(
    therapistQuery.getComment(id, userRole),
  );
  const { control, handleSubmit } = useForm<NoteInputField>({
    resolver: yupResolver(schema),
    values: {
      content: noteInfo?.content || '',
    },
    mode: 'onTouched',
  });

  const { mutateAsync: updateCommentNote, isLoading } = useMutate<NoteInputField>(
    therapistQuery.updateComment,
  );

  const handleUpdateComment: SubmitHandler<NoteInputField> = (values) => {
    const payload = {
      ...values,
      user: {
        id,
        role: userRole,
      },
    };

    updateCommentNote(payload, {
      onSuccess: () => {
        refetchNote();
        notification.success({ message: 'メモ・共有事項を保存しました' });
      },
    });
  };

  return (
    <Card>
      <div className="wrapper max-w-[860px] w-full p-16 ">
        <Form className="flex flex-col gap-1" onFinish={handleSubmit(handleUpdateComment)}>
          <TextAreaInput
            control={control}
            label=""
            maxLength={500}
            name="content"
            placeholder="自由入力"
            rows={10}
          />

          <div className="flex justify-end">
            <Space>
              <Button
                className="w-full max-w-xs"
                htmlType="submit"
                loading={isLoading}
                size="large"
                type="primary"
              >
                {t('global:save')}
              </Button>
            </Space>
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default NotesForm;
