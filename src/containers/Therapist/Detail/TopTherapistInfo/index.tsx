import { LockFilled, UserOutlined } from '@ant-design/icons';
import { Avatar, Badge, Divider, Space, Spin } from 'antd';
import { useFetch } from 'hooks';
import { SnoozeIcon } from 'icons';
import therapistQuery from 'models/therapist';
import type { ITherapist } from 'models/therapist/types';
import { useParams } from 'react-router';
import { THERAPIST_STATUS } from 'utils/constants';

const TopTherapistInfo = () => {
  const { id = '' } = useParams();
  const { data: therapist, isLoading } = useFetch<ITherapist>(
    therapistQuery.profileDetail(id as string),
  );
  const renderStatusIcon = () => {
    switch (therapist?.status) {
      case THERAPIST_STATUS.LOCKED:
        return (
          <div className="h-6 w-6 bg-[red] rounded-full flex justify-center items-center border-[solid_1px_white]">
            <LockFilled className="text-white text-base" />
          </div>
        );
      case THERAPIST_STATUS.INACTIVE:
        return (
          <div className="h-6 w-6 bg-[grey] rounded-full flex justify-center items-center border-[solid_1px_white]">
            <img alt="status" src={SnoozeIcon} style={{ width: 12 }} />
          </div>
        );
      default:
        return 0;
    }
  };
  return (
    <>
      <Divider className="m-0" />
      <Space className="py-6" size={20}>
        <Badge count={renderStatusIcon()} offset={[-8, 50]}>
          <Avatar icon={<UserOutlined />} size={64} src={therapist?.profilePicture?.url} />
        </Badge>
        <Space direction="vertical">
          {isLoading ? (
            <Spin />
          ) : (
            <>
              <div className="text-lg">{`${therapist?.fullName} (${therapist?.nickName})`} </div>
              <div>{therapist?._id}</div>
            </>
          )}
        </Space>
      </Space>
      <Divider className="m-0" />
    </>
  );
};

export default TopTherapistInfo;
