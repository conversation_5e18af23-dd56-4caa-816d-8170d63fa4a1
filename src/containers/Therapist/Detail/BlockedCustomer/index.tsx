import type { ProColumns } from '@ant-design/pro-table';
import { Card, Typography } from 'antd';
import CustomTable from 'components/CustomTable';
import useList from 'hooks/useList';
import therapistQuery from 'models/therapist';
import type { IBlackListCustomer } from 'models/therapist/types';
import { useParams } from 'react-router';
import Helper from 'utils/helpers';

const { Paragraph } = Typography;

const BlackListCustomer = () => {
  const { id = '' } = useParams();

  const { list, isFetching, pagination } = useList<IBlackListCustomer>(
    therapistQuery.getBlockedCustomerList(id),
  );
  const BlockedList: ProColumns<IBlackListCustomer>[] = [
    {
      dataIndex: '_id',
      width: 200,
      title: 'ID',
      copyable: true,
    },
    {
      dataIndex: 'name',
      title: '名前',
      width: 280,
      ellipsis: true,
    },
    {
      dataIndex: 'phone',
      title: '電話番号',
      width: 200,

      render: (_, data: IBlackListCustomer) => (
        <Paragraph style={{ marginBottom: 0 }}>
          {Helper.formatPhoneNumber(`${data?.phone}`)}
        </Paragraph>
      ),
    },

    {
      dataIndex: 'email',
      width: 280,
      title: 'メールアドレス',
      ellipsis: true,
    },
  ];
  return (
    <Card>
      <CustomTable
        columns={BlockedList}
        dataSource={list}
        loading={isFetching}
        locale={{
          emptyText: () => <div className="text-lg py-32">ブロックされたお客様がありません</div>,
        }}
        pagination={pagination}
        scroll={{ x: true }}
      />
    </Card>
  );
};

export default BlackListCustomer;
