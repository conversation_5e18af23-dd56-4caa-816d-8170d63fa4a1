import { LeftOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import { Outlet, useLocation, useNavigate, useParams } from 'react-router';

import TopTherapistInfo from './TopTherapistInfo';

const TherapistDetail = () => {
  const { id } = useParams();

  const navigate = useNavigate();
  const { pathname } = useLocation();
  const tabList = [
    {
      key: 'profile-info',
      tab: 'プロフィール',
    },
    {
      key: 'schedule',
      tab: 'スケジュール',
    },
    {
      key: 'review',
      tab: 'レビュー',
    },
    {
      key: 'note',
      tab: 'メモ ・共有事項',
    },
    {
      key: 'blocked-customer',
      tab: 'ブロックしたお客様',
    },
  ];

  const handleTabChange = (key: string) => {
    navigate(`/therapist/${id}/${key}`, { replace: true });
  };

  return (
    <PageContainer
      backIcon={<LeftOutlined />}
      // breadcrumb={{
      //   items: [
      //     {
      //       title: 'セラピスト管理',
      //     },
      //   ],
      // }}
      childrenContentStyle={{ backgroundColor: '#F5F5F5' }}
      content={<TopTherapistInfo />}
      header={{
        title: 'セラピスト管理',
      }}
      onBack={() => {
        navigate(-1);
      }}
      onTabChange={handleTabChange}
      tabActiveKey={pathname.split(`/`).pop()}
      tabList={tabList}
    >
      <Outlet />
    </PageContainer>
  );
};

export default TherapistDetail;
