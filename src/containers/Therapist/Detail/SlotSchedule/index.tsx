import './style.css';

import { Card, Space, Table } from 'antd';
import dayjs from 'dayjs';
import { useFetch } from 'hooks';
import therapistQuery from 'models/therapist';
import type { IBookingOnSlotSchedule, ISlotSchedule } from 'models/therapist/types';
import { useEffect } from 'react';
import { useParams } from 'react-router';
import dayjsJA from 'utils/dayjsJA';

import { formatDataToSlotSchedule } from './helper';
import Slot from './Slot';

const CELL_WIDTH = '92px';

function makeColumns(scheduleColumns: string[]) {
  const firstCol = {
    title: '日時',
    dataIndex: 'slotName',
    width: CELL_WIDTH,
    fixed: 'left',
  };

  const cols = scheduleColumns.map((d) => ({
    title: (
      <span className={`d${dayjsJA(d, 'YYYY-MM-DD').day()}`}>
        {dayjsJA(d, 'YYYY-MM-DD').format('(ddd)\nD')}
      </span>
    ),
    width: CELL_WIDTH,
    render: ({
      dates: timeSlotSchedule_onDate,
      slotName,
    }: {
      dates: { [key: string]: 'X' | 'O' | IBookingOnSlotSchedule };
      slotName: string;
    }) => {
      const slot = timeSlotSchedule_onDate[d];
      const isGreyedOut =
        dayjs.utc(`${d} ${slotName}`).tz('Asia/Tokyo', true) <= dayjsJA().add(-30, 'm');

      return <Slot isGrey={isGreyedOut} slot={slot} />;
    },
  }));

  return [firstCol, ...cols];
}

const SlotSchedule = () => {
  const { id = '' } = useParams();

  const today = dayjsJA().startOf('D');
  const dateRange = [today, today.add(30, 'day')];

  const { data, isLoading, remove } = useFetch<ISlotSchedule[]>({
    ...therapistQuery.getTherapistSlotSchedule(id),
    customParams: {
      startTime: dateRange[0]?.toISOString(),
      endTime: dateRange[1]?.toISOString(),
    },
  });

  useEffect(() => {
    return remove;
  }, []);

  // Tried to useMemo these 2 line below but I gave worse performance :shrug:
  const { slots, slotByDate } = formatDataToSlotSchedule(data, dateRange);
  const columns = makeColumns(slotByDate);

  return (
    <Card>
      <Space align="center" direction="vertical" style={{ width: '100%', marginBottom: 16 }}>
        <b>{`${dateRange[0]?.format('YYYY/MM/DD')} ~ ${dateRange[1]?.format('YYYY/MM/DD')}`}</b>
      </Space>
      <Table
        bordered
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        columns={columns}
        dataSource={slots}
        id="slotSchedule"
        loading={isLoading}
        pagination={false}
        scroll={{ x: true }}
      />
    </Card>
  );
};

export default SlotSchedule;
