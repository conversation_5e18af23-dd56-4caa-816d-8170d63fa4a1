import type { IBookingOnSlotSchedule } from 'models/therapist/types';
import React from 'react';
import dayjsJA from 'utils/dayjsJA';

import { IconO, IconX } from './icons';

const CELL_HIGHT = 55;

const Slot = ({
  slot = 'X',
  isGrey = false,
}: {
  slot: 'X' | 'O' | IBookingOnSlotSchedule | undefined;
  isGrey: boolean;
}) => {
  if (typeof slot === 'object') {
    const duration = dayjsJA(slot.end).diff(slot.start, 'minutes');
    const durationBySlot = duration / 30 - 1;

    const backgroundColor = slot.category === 'REQUEST' ? '#ff3b3b' : '#ffd280';

    let transform = 'translate(-50%, -12px)';
    if (slot.isBookingEndMark) {
      transform = `translate(-50%, calc(-100% + 12px))`;
    }

    return (
      <div className={isGrey ? 'grey' : ''} style={{ position: 'relative' }}>
        <span
          style={{
            zIndex: 1,
            width: 28,
            height: CELL_HIGHT * durationBySlot + 28,
            backgroundColor,
            content: '',
            position: 'absolute',
            borderRadius: 14,
            transform,
          }}
        />
      </div>
    );
  }

  return (
    <div className={`slot-x-o ${isGrey ? 'grey' : ''}`}>
      {slot === 'O' && <IconO />}
      {slot === 'X' && <IconX />}
    </div>
  );
};

export default Slot;
