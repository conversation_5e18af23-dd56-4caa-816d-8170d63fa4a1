import type { ConfigType as DayjsAble, Dayjs } from 'dayjs';
import { flow, isEmpty, map, mapKeys, mapValues } from 'lodash';
import type {
  IBookingOnSlotSchedule,
  ISlotSchedule,
  ISlotScheduleWorkingTime,
} from 'models/therapist/types';
import dayjsJA from 'utils/dayjsJA';

const SLOT_SELECTIONS = [
  '00:00',
  '00:30',
  '01:00',
  '01:30',
  '02:00',
  '02:30',
  '03:00',
  '03:30',
  '04:00',
  '04:30',
  '05:00',
  '05:30',
  '06:00',
  '06:30',
  '07:00',
  '07:30',
  '08:00',
  '08:30',
  '09:00',
  '09:30',
  '10:00',
  '10:30',
  '11:00',
  '11:30',
  '12:00',
  '12:30',
  '13:00',
  '13:30',
  '14:00',
  '14:30',
  '15:00',
  '15:30',
  '16:00',
  '16:30',
  '17:00',
  '17:30',
  '18:00',
  '18:30',
  '19:00',
  '19:30',
  '20:00',
  '20:30',
  '21:00',
  '21:30',
  '22:00',
  '22:30',
  '23:00',
  '23:30',
];

function generateDateList(startDate: DayjsAble, endDate: DayjsAble) {
  const dateList = [];
  let currentDate = dayjsJA(startDate);

  while (currentDate.isBefore(dayjsJA(endDate).clone().add(1, 'day'))) {
    dateList.push(currentDate.locale('ja').format('YYYY-MM-DD'));
    currentDate = currentDate.add(1, 'day');
  }

  return dateList;
}

export function formatDataToSlotSchedule(data: ISlotSchedule[] | undefined, dateRange: Dayjs[]) {
  if (!data) {
    return {
      slotByDate: [],
      slots: [],
    };
  }

  if (isEmpty(data)) {
    return {
      slotByDate: generateDateList(dateRange[0], dateRange[1]),
      slots: SLOT_SELECTIONS.map((s) => ({ dates: {}, slotName: s })),
    };
  }

  const result = flow(
    (d) => mapKeys(d, (_val, key) => dayjsJA(key).format('YYYY-MM-DD')),
    (d) =>
      mapValues(d, (val, key) => {
        const timeSlotMap: any = {};

        const { workingTimes, busyTimeslots } = val;
        workingTimes.forEach((WTPart: ISlotScheduleWorkingTime) => {
          let min30Interval = dayjsJA(WTPart.start);
          while (min30Interval.isBefore(dayjsJA(WTPart.end))) {
            timeSlotMap[min30Interval.format('HH:mm')] = 'O';
            min30Interval = min30Interval.add(30, 'minutes');
          }
        });

        busyTimeslots.forEach((booking: IBookingOnSlotSchedule) => {
          let min30Interval = dayjsJA(booking.start).add(-1, 'hour'); // gap time 2 slot before, after booking
          while (min30Interval.isBefore(dayjsJA(booking.end).add(1, 'hour'))) {
            if (min30Interval.format('YYYY-MM-DD') === key) {
              // only apply on today, process by each day, server will the turn that one booking in 2 days if that booking lasting from a day to its tomorrow

              if (min30Interval.isSame(dayjsJA(booking.start))) {
                timeSlotMap[min30Interval.format('HH:mm')] = booking;
              } else if (min30Interval.isSame(dayjsJA(booking.end).add(-30, 'minutes'))) {
                const bookingTail: IBookingOnSlotSchedule = {
                  ...booking,
                  isBookingEndMark: true,
                };
                timeSlotMap[min30Interval.format('HH:mm')] = bookingTail;
              } else {
                timeSlotMap[min30Interval.format('HH:mm')] = 'X';
              }
            }

            min30Interval = min30Interval.add(30, 'minutes');
          }
        });

        return {
          ...timeSlotMap,
        };
      }),
  )(data);

  const output: {
    [key: string]: {
      [innerKey: string]: any;
    };
  } = {};

  SLOT_SELECTIONS.forEach((key) => {
    output[key] = {};

    Object.keys(result).forEach((innerKey) => {
      if (result[innerKey][key]) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        output[key][innerKey] = result[innerKey][key];
      }
    });
  });

  return {
    slotByDate: generateDateList(dateRange[0], dateRange[1]),
    slots: map(output, (v, k) => ({ dates: v, slotName: k })),
  };
}
