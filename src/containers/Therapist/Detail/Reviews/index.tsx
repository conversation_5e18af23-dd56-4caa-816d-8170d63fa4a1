import { Col, Row } from 'antd';
import BigSpin from 'components/BigSpin';
import { useFetch } from 'hooks';
import therapistQuery from 'models/therapist';
import type { ITherapistReview } from 'models/therapist/types';
import { useEffect } from 'react';
import { useParams } from 'react-router';

import ReviewCommentList from './ReviewCommentList';
import ReviewRate from './ReviewRate';

const TherapistReview = () => {
  const { id = '' } = useParams();

  const {
    data: reviewInfo,
    isLoading,
    remove,
  } = useFetch<ITherapistReview>(therapistQuery.getReviewOfTherapist(id));
  // clear old data when unmount
  useEffect(() => {
    return remove;
  }, []);

  if (isLoading) {
    return <BigSpin />;
  }

  return (
    <div className="therapist-review">
      <Row gutter={[24, 12]}>
        <Col span={6}>
          <ReviewRate reviewInfo={reviewInfo} />
        </Col>
        <Col span={18}>
          <ReviewCommentList reviewInfo={reviewInfo} />
        </Col>
      </Row>
    </div>
  );
};

export default TherapistReview;
