/* eslint-disable no-unsafe-optional-chaining */
import { StarFilled } from '@ant-design/icons';
import { Card, Descriptions, Progress, Space } from 'antd';
import type { ITherapistReview } from 'models/therapist/types';
import Helper from 'utils/helpers';

export type ReviewRateProps = {
  reviewInfo?: ITherapistReview;
};
const ReviewRate = ({ reviewInfo }: ReviewRateProps) => {
  const totalAverage = reviewInfo?.summaryReview?.sumRating
    ? (reviewInfo.summaryReview.sumRating / reviewInfo.summaryReview.sumReviewer).toFixed(1)
    : 0;
  const Rate1Star = reviewInfo?.detail.find((r) => r.rating === 1);
  const Rate2Star = reviewInfo?.detail.find((r) => r.rating === 2);
  const Rate3Star = reviewInfo?.detail.find((r) => r.rating === 3);
  const Rate4Star = reviewInfo?.detail.find((r) => r.rating === 4);
  const Rate5Star = reviewInfo?.detail.find((r) => r.rating === 5);

  return (
    <Card title="星評価">
      <Descriptions colon={false} column={1} size="small">
        <Descriptions.Item contentStyle={{ display: 'flex', justifyContent: 'center' }}>
          <Space>
            <span className="text-5xl font-bold text-[#324452]">{totalAverage}</span>
            <StarFilled className="text-4xl text-[#EBB856] mt-2" />
          </Space>
        </Descriptions.Item>
        <Descriptions.Item contentStyle={{ display: 'flex', justifyContent: 'center' }}>
          <span className="font-bold text-[#EBB856]">
            {reviewInfo?.summaryReview?.sumReviewer || 0} 件のレビュー
          </span>
        </Descriptions.Item>
        <Descriptions.Item label={<div className="font-medium">星 5</div>}>
          <Progress
            format={(percent) => Helper.formatRatePercent(percent || 0)}
            percent={Helper.roundRating(Rate5Star?.count, reviewInfo?.summaryReview?.sumReviewer)}
            strokeColor="#EBB856"
          />
        </Descriptions.Item>
        <Descriptions.Item label={<div className="font-medium">星 4</div>}>
          <Progress
            format={(percent) => Helper.formatRatePercent(percent || 0)}
            percent={Helper.roundRating(Rate4Star?.count, reviewInfo?.summaryReview?.sumReviewer)}
            strokeColor="#EBB856"
          />
        </Descriptions.Item>
        <Descriptions.Item label={<div className="font-medium">星 3</div>}>
          <Progress
            format={(percent) => Helper.formatRatePercent(percent || 0)}
            percent={Helper.roundRating(Rate3Star?.count, reviewInfo?.summaryReview?.sumReviewer)}
            strokeColor="#EBB856"
          />
        </Descriptions.Item>
        <Descriptions.Item label={<div className="font-medium">星 2</div>}>
          <Progress
            format={(percent) => Helper.formatRatePercent(percent || 0)}
            percent={Helper.roundRating(Rate2Star?.count, reviewInfo?.summaryReview?.sumReviewer)}
            strokeColor="#EBB856"
          />
        </Descriptions.Item>
        <Descriptions.Item label={<div className="font-medium">星 1</div>}>
          <Progress
            format={(percent) => Helper.formatRatePercent(percent || 0)}
            percent={Helper.roundRating(Rate1Star?.count, reviewInfo?.summaryReview?.sumReviewer)}
            strokeColor="#EBB856"
          />
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default ReviewRate;
