import { UserOutlined } from '@ant-design/icons';
import { Avatar, Card, Empty, List, Rate, Skeleton, Space, Tag, Typography } from 'antd';
import dayjs from 'dayjs';
import type { IReviewItem, ITherapistReview } from 'models/therapist/types';
import { DateFormat } from 'utils/constants';

const { Text } = Typography;
export type ReviewRateProps = {
  reviewInfo?: ITherapistReview;
};
const ReviewCommentList = ({ reviewInfo }: ReviewRateProps) => {
  const reviewersList = reviewInfo?.reviews?.data;
  if (reviewersList?.length === 0) {
    return (
      <Card title="レビュー内容">
        <Empty description="レビューなし" />
      </Card>
    );
  }
  return (
    <Card title="レビュー内容">
      <List
        dataSource={reviewersList}
        pagination={{
          showSizeChanger: false,
          pageSize: 10,
        }}
        renderItem={(item: IReviewItem) => (
          <List.Item key={item?._id}>
            <Skeleton active avatar loading={false} title={false}>
              <List.Item.Meta
                avatar={
                  <Avatar
                    icon={<UserOutlined />}
                    size={'large'}
                    src={item?.reviewer?.profilePicture?.url}
                  />
                }
                description={
                  <div className="flex justify-between">
                    <Space direction="vertical">
                      <Tag color="default">#{item?.bookingId}</Tag>
                      <Rate defaultValue={item?.rating} disabled />
                      <div>
                        <Text className="whitespace-pre-line block">
                          {item?.comment?.therapist}
                        </Text>
                      </div>
                    </Space>
                    <div className="ml-8 text-[#BDC4CB] min-w-[120px] font-medium">
                      {dayjs(item?.createdAt).format(DateFormat.YEAR_MONTH_DATE_JP)}
                    </div>
                  </div>
                }
                title={item?.reviewer?.name}
              />
            </Skeleton>
          </List.Item>
        )}
      />
    </Card>
  );
};

export default ReviewCommentList;
