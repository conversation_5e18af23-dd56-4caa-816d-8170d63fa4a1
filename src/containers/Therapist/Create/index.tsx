import { useQueryClient } from '@tanstack/react-query';
import { notification } from 'antd';
import { useMutate } from 'hooks';
import therapistQuery from 'models/therapist';

import TherapistInputForms from '../TherapistInputForm';
import type { TherapistInputFields } from '../TherapistInputForm/schema';

const CreateTherapistComponent = ({ onCancel }: { onCancel: () => void }) => {
  const queryClient = useQueryClient();

  const { mutateAsync: create, isLoading } = useMutate<TherapistInputFields>({
    ...therapistQuery.create,
  });

  const handleSubmit = ({ phoneCode, phone, ...otherValues }: TherapistInputFields) => {
    const params = {
      ...otherValues,
      phone: `+${phoneCode}${phone}`,
    };
    create(params, {
      onSuccess: () => {
        notification.success({ message: 'セラピストアカウントを作成しました' });
        queryClient.invalidateQueries(therapistQuery.list);
        setTimeout(onCancel, 500);
      },
    });
  };

  return (
    <div>
      <TherapistInputForms loading={isLoading} onCancel={onCancel} onSubmit={handleSubmit} />
    </div>
  );
};

export default CreateTherapistComponent;
