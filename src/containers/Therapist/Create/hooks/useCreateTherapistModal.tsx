import type { ModalFuncProps } from 'antd';
import { Modal } from 'antd';
import { useEffect } from 'react';

import CreateTherapistComponent from '..';

const useCreateTherapistModal = () => {
  const [modal, context] = Modal.useModal();
  const { destroyAll } = Modal;

  useEffect(() => {
    return () => destroyAll();
  }, [destroyAll]);

  const createTherapistModal = (options?: ModalFuncProps) => {
    const customModal = modal.confirm({
      icon: null,
      centered: true,
      className: 'min-w-[360px] md:min-w-[680px]',
      maskClosable: false,
      footer: false,
      ...options,
    });
    customModal.update({
      content: <CreateTherapistComponent onCancel={customModal.destroy} />,
    });

    return customModal;
  };
  return { context, createTherapistModal };
};

export default useCreateTherapistModal;
