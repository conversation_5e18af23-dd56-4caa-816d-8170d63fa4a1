import 'antd-country-phone-input/dist/index.css';

import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ConfigProvider, notification } from 'antd';
import en_US from 'antd/locale/en_US';
import ja_<PERSON> from 'antd/locale/ja_JP';
import { ConfigProvider as PhoneConfig } from 'antd-country-phone-input';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import useGlobalState from 'hooks/useGlobalState';
import { useEffect } from 'react';
import { useLocation } from 'react-router';
import { Outlet } from 'react-router-dom';
import ja from 'world_countries_lists/data/countries/ja/world.json';

dayjs.extend(utc);
dayjs.extend(timezone);

notification.config({
  placement: 'bottomRight',
  duration: 3,
});

const locales = {
  jp: ja_<PERSON>,
  en: en_US,
};

const App = () => {
  const { key } = useLocation();
  const { lng } = useGlobalState();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [key]);

  return (
    <ConfigProvider
      autoInsertSpaceInButton={false}
      locale={locales[lng as keyof typeof locales]}
      theme={{
        token: {
          colorError: '#db5a42',
          colorText: '#333333',
          fontFamily: 'Noto Sans JP,sans-serif',
          fontSize: 14,
          motionDurationSlow: '0.1s',
          borderRadius: 12,
          controlOutlineWidth: 0,
        },
      }}
    >
      <PhoneConfig locale={ja}>
        <Outlet />
        <ReactQueryDevtools initialIsOpen={true} />
      </PhoneConfig>
    </ConfigProvider>
  );
};

export default App;
