import './style.css';

import { CaretLeftOutlined, CaretRightOutlined, UserOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-table';
import { Avatar, Button, Form, Space, Typography } from 'antd';
import type { RangePickerProps } from 'antd/es/date-picker';
import CustomTable from 'components/CustomTable';
import DateInput from 'components/Form/DateInput';
import SearchWrapper from 'components/SearchWrapper';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import useList from 'hooks/useList';
import salonQuery from 'models/salons';
import type { IDataSourceItem, ITherapistScheduleItem } from 'models/salons/type';
import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import dayjsJA from 'utils/dayjsJA';
import Helper from 'utils/helpers';

import UnScheduleIcon from './IconComponents/Unschedule';
import UnScheduleIconSmall from './IconComponents/UnScheduleSmall';
import WorkingTimeIcon from './IconComponents/WorkingTime';
import WorkingTimeIconSmall from './IconComponents/WorkingTimeSmall';
import schema from './schema';

const tableScrollHeightArea = window.innerHeight - 390; // px
const minDate = dayjsJA().startOf('D');
const maxDate = dayjsJA().startOf('D').add(30, 'days');
const disabledDate: RangePickerProps['disabledDate'] = (current) => {
  return (current && current < minDate) || (current && current > maxDate);
};
const { Text } = Typography;

const COL_SLOT_CELL_WIDTH = 40;
const HOURLY_CELL_WIDTH = 82; // 1 hour 2 slot, each slot 40px, +2px border each size

const WorkingSchedule = () => {
  const [selectedDate, setSelectedDate] = useState<Dayjs>(dayjsJA().startOf('D'));
  const params = {
    startTime: dayjs(selectedDate).startOf('D').set('minute', 1).toISOString(),
    endTime: dayjs(selectedDate).endOf('D').toISOString(),
    limit: 20,
  };
  const { list, pagination, isFetching } = useList<ITherapistScheduleItem>({
    ...salonQuery.getWorkingSchedule(),
    customParams: params,
  });

  const handleNextDay = () => {
    setSelectedDate(dayjs(selectedDate).add(1, 'day'));
  };
  const handlePreDate = () => {
    setSelectedDate(dayjs(selectedDate).subtract(1, 'day'));
  };

  useEffect(() => {
    function scrollToDate() {
      const [element] = document.querySelectorAll('#all-therapist-schedule div.ant-table-body');

      if (element) {
        element.scrollTo({
          top: 0,
          left: dayjsJA().isSame(selectedDate, 'D')
            ? dayjsJA().get('hours') * HOURLY_CELL_WIDTH
            : 0,
          behavior: 'smooth',
        });
      }
    }

    scrollToDate();
  }, [list]);

  // Declare column
  const columns: ProColumns<any>[] = [
    {
      title: 'セラピスト',
      fixed: 'left',
      dataIndex: 'therapist',
      width: 260,
      render: function renderItem(record: any) {
        return (
          <div className=" pl-4">
            <Link target="_blank" to={`/therapist/${record?.therapistId}/profile-info`}>
              <div className="flex items-center">
                <Avatar
                  className="mr-2 min-w-40"
                  icon={<UserOutlined />}
                  size="large"
                  src={record?.photo}
                />
                <div style={{ overflow: 'hidden', paddingRight: 8, color: 'black' }}>
                  {record?.therapistName}
                  {/* {record.isNew && (
                    <div className="icon-new-therapist">
                      <span className="IconBlink">
                        <IconBlink />
                      </span>
                      New
                    </div>
                  )} */}
                </div>
              </div>
            </Link>
          </div>
        );
      },
    },
  ];

  // Declare time columns 0h => 24h: 48 slot
  for (let index = 0; index < 48; index += 1) {
    const dataIndex = Helper.getDataIndexFromIndex(index);

    columns.push({
      title: (
        <div className="font-bold whitespace-nowrap">
          {Helper.padStartTime(Math.floor(dataIndex / 100))}:{Helper.padStartTime(dataIndex % 100)}
        </div>
      ),
      dataIndex,
      colSpan: dataIndex % 100 === 0 ? 2 : 0,
      align: 'center',
      width: COL_SLOT_CELL_WIDTH,
      render: (value: any) => {
        return value === true ? <WorkingTimeIcon /> : <UnScheduleIcon />;
      },
    });
  }

  return (
    <div id="therapist-schedule">
      <Form>
        <div className="flex justify-between">
          <Space>
            <UnScheduleIconSmall />
            <Text className="mr-8">稼働時間外</Text>
            <WorkingTimeIconSmall />
            <Text>稼働時間</Text>
          </Space>
          <SearchWrapper schema={schema}>
            {({ control }) => (
              <div className="flex gap-1">
                <Button disabled={selectedDate <= dayjs()} onClick={handlePreDate}>
                  <CaretLeftOutlined />
                </Button>
                <DateInput
                  allowClear={false}
                  control={control}
                  disabledDate={disabledDate}
                  name="date"
                  onChange={(date) => {
                    setSelectedDate(dayjs(date));
                  }}
                  value={selectedDate}
                />
                <Button disabled={selectedDate >= maxDate} onClick={handleNextDay}>
                  <CaretRightOutlined />
                </Button>
              </div>
            )}
          </SearchWrapper>
        </div>
      </Form>
      <CustomTable
        bordered
        columns={columns}
        dataSource={list.map((item: ITherapistScheduleItem) => {
          // therapist info data
          const data: IDataSourceItem = {
            key: item._id,
            therapist: {
              photo: item.profilePicture?.url,
              therapistId: item?._id,
              therapistName: item?.fullName,
              isNew: item?.isNew,
            },
          };
          const schedules = item?.timeslot[0]?.workingTimes; // timeslot [0]: case: search 1 day.

          // Time data
          (schedules || []).forEach((schedule) => {
            const startAt = dayjs(schedule.start);
            const endAt = dayjs(schedule.end);

            for (
              let index = Helper.getIndexFromMoment(startAt);
              index < (Helper.getIndexFromMoment(endAt) || 48);
              index += 1
            ) {
              const dataIndex = Helper.getDataIndexFromIndex(index);

              data[dataIndex] = true;
            }
          });
          return data;
        })}
        id="all-therapist-schedule"
        loading={isFetching}
        pagination={pagination}
        scroll={{ x: 'max-content', y: tableScrollHeightArea }}
      />
    </div>
  );
};

export default WorkingSchedule;
