import { <PERSON>Container } from '@ant-design/pro-layout';
import { useFetch, useMutate } from 'hooks';
import exampleQuery from 'models/example';
import type { IExample } from 'models/example/types';
import { useNavigate, useParams } from 'react-router';

import ExampleInputForms from '../ExampleInputForm';
import type { ExampleInputFields } from '../ExampleInputForm/schema';

const ExampleEdit = () => {
  const { id = '' } = useParams();
  const navigate = useNavigate();
  const { data: exampleDetail, refetch: refetchDetail } = useFetch<IExample>(
    exampleQuery.detail(id),
  );
  const {
    mutateAsync: update,
    isLoading: isUpdating,
    isSuccess,
  } = useMutate<ExampleInputFields>(exampleQuery.update(id));

  const handleUpdate = (values: ExampleInputFields, isDirty: boolean) => {
    if (isDirty) {
      update(values, {
        onSuccess: () => {
          refetchDetail();
          navigate(-1);
        },
      });
    } else {
      navigate(-1);
    }
  };

  return (
    <PageContainer>
      <div className="page-title">Edit</div>
      <ExampleInputForms
        isSuccess={isSuccess}
        loading={isUpdating}
        onSubmit={handleUpdate}
        values={{
          name: exampleDetail?.name || '',
          address: exampleDetail?.address || '',
          tel: exampleDetail?.tel || '',
          email: exampleDetail?.email || '',
          job: exampleDetail?.job || '',
          sex: exampleDetail?.sex || '',
          status: exampleDetail?.status || '',
        }}
      />
    </PageContainer>
  );
};

export default ExampleEdit;
