import { Modal } from 'antd';
import ExampleInputForms from 'containers/Example/ExampleInputForm';
import type { ExampleInputFields } from 'containers/Example/ExampleInputForm/schema';
import { useMutate } from 'hooks';
import useFetch from 'hooks/useFetch';
import exampleQuery from 'models/example';
import type { IExample } from 'models/example/types';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

type ExampleModalEditProps = {
  id: string;
  closeModal: () => void;
};
const ExampleModalEdit = ({ id, closeModal }: ExampleModalEditProps) => {
  const { data: exampleDetail, refetch: refetchDetail } = useFetch<IExample>(
    exampleQuery.detail(id),
  );
  const {
    mutateAsync: update,
    isLoading: isUpdating,
    isSuccess,
  } = useMutate<ExampleInputFields>(exampleQuery.update(id));

  const handleUpdate = (values: ExampleInputFields, isDirty: boolean) => {
    if (isDirty) {
      update(values, {
        onSuccess: () => {
          refetchDetail();
          closeModal();
        },
      });
    } else {
      closeModal();
    }
  };

  return (
    <ExampleInputForms
      isSuccess={isSuccess}
      loading={isUpdating}
      onSubmit={handleUpdate}
      values={{
        name: exampleDetail?.name || '',
        address: exampleDetail?.address || '',
        tel: exampleDetail?.tel || '',
        email: exampleDetail?.email || '',
        job: exampleDetail?.job || '',
        sex: exampleDetail?.sex || '',
        status: exampleDetail?.status || '',
      }}
    />
  );
};

const useExampleModalEdit = () => {
  const { t } = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState<string | null>(null);
  const openModal = (id: string) => {
    setIsModalOpen(id);
  };

  const closeModal = () => {
    setIsModalOpen(null);
  };
  const ModalComponent = () => (
    <Modal
      destroyOnClose
      footer={null}
      onCancel={closeModal}
      open={!!isModalOpen}
      title={t('inquiry:editBasicInformation')}
      width={768}
    >
      {!!isModalOpen && <ExampleModalEdit closeModal={closeModal} id={isModalOpen} />}
    </Modal>
  );

  return { ModalComponent, openModal, closeModal };
};

export default useExampleModalEdit;
