import { Modal } from 'antd';
import CustomDescription from 'components/CustomDescription';
import useFetch from 'hooks/useFetch';
import exampleQuery from 'models/example';
import type { IExample } from 'models/example/types';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { exampleFields } from '../fields';

const ExampleModalDetail = ({ id }: { id: string }) => {
  const { data } = useFetch<IExample>(exampleQuery.detail(id));

  const fields = [
    exampleFields.id,
    exampleFields.name,
    exampleFields.email,
    exampleFields.job,
    exampleFields.sex,
    exampleFields.tel,
    exampleFields.address,
    exampleFields.status,
    exampleFields.createdAt,
  ];

  return (
    <div>
      <CustomDescription data={data} fields={fields} layout={'vertical'} />
    </div>
  );
};

const useExampleModal = () => {
  const { t } = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState<string | null>(null);
  const openModal = (id: string) => {
    setIsModalOpen(id);
  };

  const closeModal = () => {
    setIsModalOpen(null);
  };
  const ModalDetailComponent = () => (
    <Modal
      destroyOnClose
      onCancel={closeModal}
      onOk={closeModal}
      open={!!isModalOpen}
      title={t('inquiry:editBasicInformation')}
      width={768}
    >
      {!!isModalOpen && <ExampleModalDetail id={isModalOpen} />}
    </Modal>
  );

  return { ModalDetailComponent, openModal };
};

export default useExampleModal;
