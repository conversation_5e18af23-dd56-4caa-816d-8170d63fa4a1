import type { DescriptionFields } from 'components/CustomDescription';
import dayjs from 'dayjs';
import type { IExample } from 'models/example/types';
import { DateFormat } from 'utils/constants';

export const exampleFields: Record<keyof IExample, DescriptionFields<IExample>> = {
  id: {
    label: 'ID',
    path: 'id',
  },
  name: {
    label: 'Name',
    path: 'name',
  },
  email: {
    label: 'Email',
    path: 'email',
  },
  job: {
    label: 'Job',
    path: 'job',
  },
  sex: {
    label: 'Sex',
    path: 'sex',
  },
  tel: {
    label: 'Tel',
    path: 'tel',
  },
  address: {
    label: 'Address',
    path: 'address',
  },
  status: {
    label: 'Status',
    path: 'status',
  },
  createdAt: {
    label: 'Created At',
    path: 'createdAt',
    render: (data) => dayjs(data.createdAt).format(DateFormat.YEAR_MONTH_DATE_JP),
  },
};
