import { <PERSON>Container } from '@ant-design/pro-layout';
import { <PERSON><PERSON>, <PERSON> } from 'antd';
import CustomDescription from 'components/CustomDescription';
import { useFetch } from 'hooks';
import exampleQuery from 'models/example';
import type { IExample } from 'models/example/types';
import { Suspense } from 'react';
import { useParams } from 'react-router';
import { Link } from 'react-router-dom';

import useExampleModalEdit from '../Edit/hooks/useExampleModalEdit';
import { exampleFields } from './fields';

const ExampleDetail = () => {
  const { id = '' } = useParams();
  const { ModalComponent, openModal } = useExampleModalEdit();

  const { data } = useFetch<IExample>({
    ...exampleQuery.detail(id),
    suspense: true,
  });

  const fields = [
    exampleFields.id,
    exampleFields.name,
    exampleFields.email,
    exampleFields.job,
    exampleFields.sex,
    exampleFields.tel,
    exampleFields.address,
    exampleFields.status,
    exampleFields.createdAt,
  ];

  return (
    <PageContainer
      breadcrumb={{
        items: [
          {
            title: <Link to="/example">Example List</Link>,
          },
          {
            title: 'Example Detail',
          },
        ],
      }}
    >
      <div className="mt-4">
        <Suspense
          fallback={
            <div className="flex justify-center">
              <Spin />
            </div>
          }
        >
          <CustomDescription data={data} fields={fields} layout={'vertical'} />
        </Suspense>
      </div>
      <div className="flex gap-3">
        <Link to={`/example/${id}/edit`}>
          <Button type="primary">Edit</Button>
        </Link>
        <Button onClick={() => openModal(id)} type="primary">
          Modal mode
        </Button>
      </div>
      <ModalComponent />
    </PageContainer>
  );
};

export default ExampleDetail;
