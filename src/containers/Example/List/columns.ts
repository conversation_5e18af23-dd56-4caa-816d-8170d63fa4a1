import type { ProColumns } from '@ant-design/pro-table';
import dayjs from 'dayjs';
import { t } from 'i18next';
import type { IExample } from 'models/example/types';
import { DateFormat } from 'utils/constants';

type ExampleListColumns = Pick<IExample, 'id' | 'name' | 'email' | 'tel' | 'status' | 'createdAt'>;

export const columns: Record<keyof ExampleListColumns, ProColumns> = {
  id: {
    dataIndex: 'id',
    title: t('common:id'),
    sorter: true,
  },
  name: {
    dataIndex: 'name',
    title: t('common:fullName'),
  },
  email: {
    dataIndex: 'email',
    title: t('common:email'),
  },
  tel: {
    dataIndex: 'tel',
    title: t('common:tel'),
  },
  status: {
    dataIndex: 'status',
    title: t('adminAccount:status'),
    filters: true,
    valueEnum: {
      ACTIVE: { text: 'Active', status: 'Success' },
      INACTIVE: { text: 'Inactive', status: 'Error' },
      DELETED: { text: 'Deleted', status: 'Error' },
    },
    render: (_, data) => data.status,
  },
  createdAt: {
    dataIndex: 'createdAt',
    title: t('adminAccount:createdAt'),
    render: (_, data) => dayjs(data.createdAt).format(DateFormat.YEAR_MONTH_DATE_JP),
  },
};
