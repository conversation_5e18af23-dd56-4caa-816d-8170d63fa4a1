import { EyeOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns } from '@ant-design/pro-table';
import { Button } from 'antd';
import CustomTable from 'components/CustomTable';
import TextInput from 'components/Form/TextInput';
import SearchWrapper from 'components/SearchWrapper';
import useList from 'hooks/useList';
import exampleQuery from 'models/example';
import type { IExample } from 'models/example/types';
import { useTranslation } from 'react-i18next';
import { Link, useLocation } from 'react-router-dom';

import useExampleModal from '../Detail/hooks/useExampleModal';
import { columns } from './columns';
import schema from './searchSchema';

const ExampleList = () => {
  const { t } = useTranslation();
  const { list, isFetching, pagination } = useList<IExample>(exampleQuery.list);
  const { pathname } = useLocation();
  const { ModalDetailComponent, openModal } = useExampleModal();

  const exampleColumns: ProColumns<IExample>[] = [
    columns.id,
    columns.name,
    columns.email,
    columns.tel,
    columns.status,
    columns.createdAt,
    {
      key: 'action',
      render: (_, data) => (
        <div className="flex gap-2">
          <Link to={`${pathname}/${data.id}`}>
            <Button icon={<EyeOutlined />} type="primary">
              {t('global:view')}
            </Button>
          </Link>
        </div>
      ),
    },
    {
      key: 'action',
      render: (_, data) => (
        <div className="flex gap-2">
          <Button icon={<EyeOutlined />} onClick={() => openModal(data.id)} type="primary">
            Open modal
          </Button>
        </div>
      ),
    },
  ];

  return (
    <PageContainer
      header={{
        title: '',
      }}
    >
      <div className="page-title">Example Page</div>
      <SearchWrapper schema={schema}>
        {({ control }) => (
          <div className="flex gap-1 max-w-md ">
            <TextInput
              allowClear
              className="w-full"
              control={control}
              name="keyword"
              placeholder="Search keyword here..."
              size="large"
            />
            <Button form="search-form" htmlType="submit" size="large" type="primary">
              {t('global:search')}
            </Button>
          </div>
        )}
      </SearchWrapper>
      <CustomTable
        columns={exampleColumns}
        dataSource={list}
        loading={isFetching}
        pagination={pagination}
      />
      <ModalDetailComponent />
    </PageContainer>
  );
};

export default ExampleList;
