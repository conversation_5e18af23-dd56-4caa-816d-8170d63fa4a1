import { t } from 'i18n';
import { Status } from 'utils/types';
import type { InferType } from 'yup';
import { mixed, object, string } from 'yup';

const schema = object({
  name: string().trim().required().max(30),
  email: string().trim().max(256).email(t('validation:invalidField')).lowercase(),
  job: string().trim().max(30),
  sex: mixed().oneOf(Object.values({ MALE: 'MALE', FEMALE: 'FEMALE' })),
  address: string().trim().max(100),
  status: mixed().oneOf(Object.values(Status)),
  tel: string().trim().max(30),
});

export type ExampleInputFields = InferType<typeof schema>;
export default schema;
