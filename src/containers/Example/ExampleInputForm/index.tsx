import { yupResolver } from '@hookform/resolvers/yup';
import { Button, Form } from 'antd';
import RadioField from 'components/Form/Radio';
import SelectInput from 'components/Form/SelectInput';
import TextInput from 'components/Form/TextInput';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { unstable_usePrompt } from 'react-router-dom';
import Helper from 'utils/helpers';

import type { ExampleInputFields } from './schema';
import schema from './schema';

export type ExampleInputFormsProps = {
  onSubmit: (values: ExampleInputFields, isDirty: boolean) => void;
  loading?: boolean;
  values: ExampleInputFields;
  isSuccess?: boolean;
};
const ExampleInputForms = ({ onSubmit, loading, values, isSuccess }: ExampleInputFormsProps) => {
  const { t } = useTranslation();
  const {
    control,
    handleSubmit,
    formState: { isDirty },
  } = useForm<ExampleInputFields>({
    resolver: yupResolver(schema),
    mode: 'onTouched',
    values,
  });

  unstable_usePrompt({
    when: isDirty && !isSuccess && !loading,
    message: t('confirmation:discardInput.message'),
  });

  return (
    <div className="max-w-3xl mx-auto p-4">
      <Form
        className="flex flex-col gap-1"
        layout="vertical"
        onFinish={handleSubmit((data) => onSubmit(data, isDirty))}
      >
        <TextInput control={control} label="Name" maxLength={30} name="name" required />
        <TextInput control={control} label="Email" name="email" type="email" />
        <TextInput control={control} label="Job" maxLength={30} name="job" />
        <RadioField
          control={control}
          data={Helper.convertObjectToOptions({
            MALE: 'Male',
            FEMALE: 'Female',
          })}
          label="Sex"
          name="sex"
          required
        />
        <TextInput control={control} label="Tel" maxLength={100} name="tel" />
        <TextInput control={control} label="Address" maxLength={100} name="address" />
        <SelectInput
          control={control}
          data={Helper.convertObjectToOptions({
            ACTIVE: 'Active',
            INACTIVE: 'InActive',
            DELETED: 'Deleted',
          })}
          label="Status"
          name="status"
          required
        />

        <div className="flex justify-center">
          <Button
            className="w-full max-w-xs"
            htmlType="submit"
            loading={loading}
            size="large"
            type="primary"
          >
            {t('global:update')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default ExampleInputForms;
