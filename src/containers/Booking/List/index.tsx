import { CalendarOutlined, CloseCircleFilled, SearchOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import { yupResolver } from '@hookform/resolvers/yup';
import type { TabsProps } from 'antd';
import { Button, Col, DatePicker, Form, Row, Space, Tabs } from 'antd';
import TextInput from 'components/Form/TextInput';
import type { Dayjs } from 'dayjs';
import useList from 'hooks/useList';
import bookingQuery from 'models/booking';
import type { IBookingListItem } from 'models/booking/types';
import type { RangeValue } from 'rc-picker/lib/interface';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useLocation } from 'react-router';
import dayjsJA from 'utils/dayjsJA';

import BookingCustomTable from './CustomTable';
import type { BookingListType } from './function';
import { BOOKING_LIST_PARAMS, BOOKING_LIST_TABS, getListCount } from './function';
import schema from './searchSchema';

const BookingList = () => {
  const { RangePicker } = DatePicker;
  const location = useLocation();

  const { state } = location;
  const {
    category: categoryFromDetail,
    startTime: startTimeFromDetail,
    endTime: endTimeFromDetail,
    bookingId: bookingIdFromDetail,
  } = state || {};
  const {
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { isValid },
    watch,
  } = useForm<{ bookingId: string }>({ resolver: yupResolver(schema) });

  const [tab, setTab] = useState<string>(BOOKING_LIST_TABS.REQUEST);
  const [startDate, setStartDate] = useState(dayjsJA().startOf('D'));
  const [endDate, setEndDate] = useState(dayjsJA().endOf('D'));
  const [search, setSearch] = useState('');

  const params = useMemo(
    () => ({
      category: BOOKING_LIST_PARAMS[tab],
      startTime: startDate.toISOString(),
      endTime: endDate.toISOString(),
      bookingId: search,
    }),
    [endDate, search, startDate, tab],
  );

  const { list, isFetching, pagination, summary, resetPagination } = useList<IBookingListItem>({
    ...bookingQuery.list,
    customParams: params,
  });

  const items: TabsProps['items'] = [
    {
      // Requesting booking tab
      key: BOOKING_LIST_TABS.REQUEST,
      label: `予約リクエスト (${getListCount(summary, BOOKING_LIST_TABS.REQUEST)})`,
    },
    {
      // Confirmed booking tab
      key: BOOKING_LIST_TABS.CONFIRMED,
      label: `仮予約/予約完了 (${getListCount(summary, BOOKING_LIST_TABS.CONFIRMED)})`,
    },
    {
      // Completed payment tab
      key: BOOKING_LIST_TABS.DONE,
      label: `お会計完了 (${getListCount(summary, BOOKING_LIST_TABS.DONE)})`,
    },
    {
      // Cancelled booking tab
      key: BOOKING_LIST_TABS.CANCELED,
      label: `キャンセル (${getListCount(summary, BOOKING_LIST_TABS.CANCELED)})`,
    },
  ];

  const onInitDate = () => {
    setStartDate(dayjsJA().startOf('day'));
    setEndDate(dayjsJA().endOf('day'));
  };

  const onRangePickerChange = (values: RangeValue<Dayjs>) => {
    if (values && values[0] !== null && values[1] !== null) {
      setStartDate(dayjsJA(values[0]));
      setEndDate(dayjsJA(values[1]).endOf('day'));
    } else {
      onInitDate();
    }
  };
  const onChangeTabs = (key: string) => {
    setTab(key);
    setSearch('');
    reset();
    resetPagination();
  };
  const onSubmit = (values: { bookingId: string }) => {
    setSearch(values.bookingId);
  };
  useEffect(() => {
    if (search && list.length > 0) {
      summary.forEach((sum) => {
        if (sum.count !== 0 && sum._id) {
          setTab(BOOKING_LIST_TABS[sum?._id]);
        }
      });
    }
  }, [search, list, summary]);
  useEffect(() => {
    const subscription = watch((data) => {
      if (!data?.bookingId) {
        setSearch('');
      }
    });
    return () => {
      subscription.unsubscribe();
    };
  }, [watch]);

  useEffect(() => {
    if (categoryFromDetail) setTab(BOOKING_LIST_TABS[categoryFromDetail as BookingListType]);

    if (startTimeFromDetail) setStartDate(dayjsJA(startTimeFromDetail));

    if (endTimeFromDetail) setEndDate(dayjsJA(endTimeFromDetail));

    if (bookingIdFromDetail) {
      setSearch(bookingIdFromDetail);
      setValue('bookingId', bookingIdFromDetail);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <PageContainer style={{ minHeight: '100vh' }}>
        <Space className="w-full">
          <Form className="mr-12" layout="vertical" noValidate onFinish={handleSubmit(onSubmit)}>
            <Row className="max-w-lg">
              <Col span={20}>
                <TextInput
                  allowClear={{
                    clearIcon: (
                      <CloseCircleFilled
                        onClick={() => {
                          reset();
                        }}
                      />
                    ),
                  }}
                  control={control}
                  inputClassName="rounded-s-md rounded-none !border-[#d9d9d9]"
                  maxLength={100}
                  name="bookingId"
                  placeholder={'IDで検索する'}
                  showHelper={false}
                  size="large"
                />
              </Col>
              <Col span={4}>
                <Button
                  className="!rounded-e-md !rounded-s-none"
                  disabled={!isValid}
                  htmlType="submit"
                  icon={<SearchOutlined />}
                  size="large"
                  type="primary"
                >
                  検索
                </Button>
              </Col>
            </Row>
          </Form>
          <RangePicker
            allowClear={false}
            className="rounded-s-md rounded-none h-10 mb-6"
            defaultValue={[startDate, endDate]}
            onChange={onRangePickerChange}
            suffixIcon={<CalendarOutlined className="text-2xl" />}
            value={[startDate, endDate]}
          />
        </Space>
        <Tabs activeKey={tab} defaultActiveKey={tab} items={items} onChange={onChangeTabs} />
        <BookingCustomTable
          isFetching={isFetching}
          list={list}
          listParams={params}
          pagination={pagination}
          search={search}
        />
      </PageContainer>
    </>
  );
};

export default BookingList;
