import * as _ from 'lodash';
import type { IBooking, IBookingMenu, SummaryData } from 'models/booking/types';

export type BookingListType = 'REQUEST' | 'CONFIRMED' | 'DONE' | 'CANCELED';

export const BOOKING_LIST_PARAMS: { [key: string]: string } = {
  '1': 'REQUEST',
  '2': 'CONFIRMED',
  '3': 'DONE',
  '4': 'CANCELED',
};

export const BOOKING_LIST_TABS = {
  REQUEST: '1',
  CONFIRMED: '2',
  DONE: '3',
  CANCELED: '4',
};
export const BOOKING_CATEGORIES = {
  REQUEST: 'REQUEST',
  CONFIRMED: 'CONFIRMED',
  CLOSED: 'CLOSED',
};
export const BOOKING_TYPE = {
  STANDARD: 'STANDARD',
  ONDEMAND: 'ONDEMAND',
  APPOINTMENT: 'APPOINTMENT',
  NOW: 'NOW',
};

export const BOOKING_LIST_STATUS = {
  REQUEST: 'REQUEST',
  CONFIRMED: 'CONFIRMED',
  DONE: 'DONE',
  CANCELED: 'CANCELED',
};

export const BUILDING_TYPES = {
  HOUSE: '一戸建',
  APARTMENT: 'タワーマンション',
  OFFICE: 'オフィス',
  HOTEL: 'ホテル',
  OTHER: 'その他',
};

export const getListCount = (summary: SummaryData[] | undefined, bookingCategory: string) => {
  if (summary) {
    return summary?.find((item) => item._id === BOOKING_LIST_PARAMS[bookingCategory])?.count || 0;
  }
  return 0;
};

export function getTotalDuration(menus: IBookingMenu[]): string {
  let totalDuration = 0;
  menus.forEach((item) => {
    if (item.selectedOption) {
      totalDuration += item.selectedOption.duration;
    }
  });
  return `${totalDuration} 分`;
}

export const getBookingAddress = (booking: IBooking) => {
  if ([BOOKING_TYPE.STANDARD, BOOKING_TYPE.NOW].includes(booking.type)) {
    if (booking.googleAddress) return booking.googleAddress;
    const addresses: string[] = _.chain(booking)
      .pick(['address', 'buildingType', 'nameplate', 'buildingDetails', 'accessMethod'])
      .mapValues((value, key) =>
        key === 'buildingType' && value
          ? BUILDING_TYPES[value as keyof typeof BUILDING_TYPES]
          : String(value),
      )
      .values()
      .compact()
      .value();
    return addresses.join(', ');
  }
  const ward = booking.ward ? `, ${booking.ward.name}` : '';
  /* Hidden city when prefecture is tokyo and city is 23区内 */
  const city =
    booking.city &&
    !(booking.prefecture.areaCode === '0100000' && booking.city.areaCode === '0101000')
      ? `, ${booking.city.name}`
      : '';
  const prefecture = booking.prefecture ? booking.prefecture.name : '';
  const address = booking.address ? `, ${booking.address}` : '';
  return `${prefecture} ${ward} ${city} ${address}`;
};
