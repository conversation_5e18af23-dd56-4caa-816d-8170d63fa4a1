import type { ProColumns } from '@ant-design/pro-table';
import type { TablePaginationConfig } from 'antd';
import { Empty } from 'antd';
import CustomTable from 'components/CustomTable';
import type { IBookingListItem } from 'models/booking/types';
import { useLocation, useNavigate } from 'react-router';

import { columns } from './column';

type BookingListTableProps = {
  list: IBookingListItem[];
  isFetching: boolean;
  pagination: TablePaginationConfig;
  search: string;
  listParams: {
    category: string | undefined;
    startTime: string;
    endTime: string;
    bookingId: string;
  };
};

const BookingCustomTable = ({
  isFetching,
  list,
  pagination,
  search,
  listParams,
}: BookingListTableProps) => {
  const BookingColumn: ProColumns<IBookingListItem>[] = columns;
  const { pathname } = useLocation();
  const navigate = useNavigate();

  const emptyText =
    search && list.length === 0 ? '現在は予約がありません' : '該当する予約が見つかりません。';
  return (
    <div>
      <CustomTable
        columns={BookingColumn}
        dataSource={list}
        loading={isFetching}
        locale={{
          emptyText: () => (
            <div className="py-32">
              <Empty description={<div className="text-lg">{emptyText}</div>} />
            </div>
          ),
        }}
        onRow={(record) => {
          return {
            onClick: () => {
              navigate(`${pathname}/${record?._id}`, {
                state: { ...listParams, current: pagination?.current },
              });
            },
          };
        }}
        pagination={pagination}
        rowClassName="hover:cursor-pointer"
      />
    </div>
  );
};

export default BookingCustomTable;
