import { Typography } from 'antd';
import { find } from 'lodash';
import type { IBookingListItem } from 'models/booking/types';
import Helper from 'utils/helpers';

const { Paragraph } = Typography;
interface KeyValue {
  [key: string]: string;
}

export const BOOKING_STATUS: KeyValue = {
  CANCELED: 'キャンセル',
  DONE: '施術完了',
  ARRIVED: '施術中',
  CONFIRMED: '仮予約',
  DEFAULT_STATUS: 'デフォルトのステータステキスト',
  PENDING: '返答待ち',
};
export const BOOKING_STATUS_DETAIL: KeyValue = {
  CANCELED: 'キャンセル',
  DONE: '確定予約',
  ARRIVED: '施術中',
  CONFIRMED: '確定予約',
  DEFAULT_STATUS: 'デフォルトのステータステキスト',
  PENDING: 'リクエスト中',
  REQUEST: 'リクエスト中',
};
export const BOOKING_STATUS_EN = {
  CONFIRMED: 'CONFIRMED',
  NEW: 'NEW',
  DONE: 'DONE',
  PENDING: 'PENDING',
  IN_TRANSIT: 'IN_TRANSIT',
  ARRIVED: 'ARRIVED',
  CANCELED: 'CANCELED',
};
export const columns = [
  {
    dataIndex: '_id',
    width: 180,
    title: '予約番号',
    copyable: true,
  },
  // Therapist name_value
  {
    dataIndex: 'therapist?.nickName',
    title: 'セラピスト名',
    ellipsis: true,
    render: (_: any, data: IBookingListItem) => (
      <Paragraph ellipsis style={{ marginBottom: 0 }}>
        {data?.therapist?.nickName}
      </Paragraph>
    ),
  },
  // Customer name_value
  {
    dataIndex: 'customer?.name',
    title: 'お客様名',
    ellipsis: true,
    render: (_: any, data: IBookingListItem) => (
      <Paragraph ellipsis style={{ marginBottom: 0 }}>
        {data?.customer?.name}
      </Paragraph>
    ),
  },
  // Phone number_value
  {
    dataIndex: 'customer.phone',
    title: 'お客様の電話番号',
    render: (_: any, data: IBookingListItem) => (
      <Paragraph style={{ marginBottom: 0 }}>
        {Helper.formatPhoneNumber(`${data?.customer?.phone}`)}
      </Paragraph>
    ),
  },
  // Booking date and time_value
  {
    dataIndex: 'dateBooking',
    title: '予約日時',
    render: (_: any, data: IBookingListItem) => (
      <Paragraph style={{ marginBottom: 0 }}>
        {data?.dateBooking ? Helper.formatDayJST(data?.dateBooking, 'YYYY-MM-DD HH:mm') : '-'}
      </Paragraph>
    ),
  },
  // Arrival date and time_value
  {
    dataIndex: 'arrivalTime',
    title: '到着予定時刻',
    render: (_: any, data: IBookingListItem) => (
      <Paragraph style={{ marginBottom: 0 }}>
        {data.statusHistory && find(data.statusHistory, { status: BOOKING_STATUS_EN.ARRIVED })
          ? Helper.formatDayJST(
              find(data.statusHistory, { status: BOOKING_STATUS_EN.ARRIVED })!.timestamp,
              'YYYY-MM-DD HH:mm',
            )
          : '-'}
      </Paragraph>
    ),
  },
  // Complete date and time_value
  {
    dataIndex: 'finishTime',
    title: '終了時間',
    render: (_: any, data: IBookingListItem) => (
      <Paragraph style={{ marginBottom: 0 }}>
        {data?.finishTime ? Helper.formatDayJST(data?.finishTime, 'YYYY-MM-DD HH:mm') : '-'}
      </Paragraph>
    ),
  },
  // Location_value
  {
    dataIndex: 'location',
    title: '都道府県',
    ellipsis: true,
    render: (_: any, data: IBookingListItem) => {
      /* Hidden city when prefecture is tokyo and city is 23区内 */
      const address = [
        data.prefecture.name,
        !(data.prefecture.areaCode === '0100000' && data.city.areaCode === '0101000')
          ? data.city.name
          : '',
        data?.ward?.name,
        data?.district?.name,
      ].filter((item) => !!item);
      return (
        <Paragraph ellipsis style={{ marginBottom: 0 }}>
          {address.join(', ')}
        </Paragraph>
      );
    },
  },
  // Current status_value
  {
    dataIndex: 'currentStatus',
    title: 'ステータス',
    render: (_: any, data: IBookingListItem) => (
      <Paragraph style={{ marginBottom: 0 }}>
        {data?.currentStatus?.status ? BOOKING_STATUS[data?.currentStatus?.status] : '-'}
      </Paragraph>
    ),
  },
];
