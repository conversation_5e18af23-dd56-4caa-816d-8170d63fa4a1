import { LeftOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import { Col, Divider, Row, Space, Typography } from 'antd';
import BigSpin from 'components/BigSpin';
import BookingFieldValue from 'components/Booking/Detail/BookingFieldValue';
import Coupon from 'components/Booking/Detail/Coupon';
import MidNightFee from 'components/Booking/Detail/MidnigtFee';
import PaymentStatus from 'components/Booking/Detail/PaymentStatus';
import TotalPriceRow from 'components/Booking/Detail/TotalPriceRow';
import { useFetch } from 'hooks';
import { get } from 'lodash';
import bookingQuery from 'models/booking';
import type { IBooking, ICancellationData } from 'models/booking/types';
import { Fragment } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router';
import { GenderFormat } from 'utils/constants';
import Helper from 'utils/helpers';

import BookingMenu from '../../../components/Booking/Detail/BookingMenu';
import { BOOKING_STATUS_DETAIL } from '../List/column';
import { BOOKING_LIST_STATUS, getBookingAddress, getTotalDuration } from '../List/function';
import { REASON } from './function';

const { Text } = Typography;

const BookingDetail = () => {
  const { id = '' } = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  const { state } = location;
  const { category, startTime, endTime, bookingId, current } = state || {};
  const previousListPage = current || '1';

  const listParams = {
    ...(category && { category }),
    ...(startTime && { startTime }),
    ...(endTime && { endTime }),
    ...(bookingId && { bookingId }),
  };

  const { data: bookingDetail, isLoading } = useFetch<IBooking>(bookingQuery.detail(id as string));

  if (isLoading) {
    return <BigSpin />;
  }
  let totalPrice: number = 0;
  let totalDuration: number = 0;
  if (bookingDetail) {
    if (bookingDetail?.menus?.length > 0) {
      bookingDetail?.menus?.forEach((booking) => {
        totalPrice += booking?.selectedOption?.price || 0;
        totalDuration += booking?.selectedOption?.duration || 0;
      });
    }
    if (bookingDetail?.extensions?.length > 0) {
      bookingDetail?.extensions?.forEach((booking) => {
        totalPrice += booking?.selectedExtension?.price || 0;
        totalDuration += booking?.selectedExtension?.duration || 0;
      });
    }
  }
  if (bookingDetail?.coupon?.amount !== 0) {
    totalPrice -= bookingDetail?.coupon?.amount || 0;
  }
  if (bookingDetail?.midnightFee && bookingDetail?.midnightFee > 0) {
    totalPrice += bookingDetail?.midnightFee || 0;
  }
  totalPrice -= bookingDetail?.point?.used?.discount || 0;

  return (
    <PageContainer
      backIcon={<LeftOutlined />}
      content={
        <>
          <Divider className="m-0" />
          <Row className="py-6">
            <Col span={8}>
              {/* Booking ID_value */}
              <BookingFieldValue label="予約ＩＤ：" labelMinWidth={0} value={bookingDetail?._id} />
            </Col>
            <Col span={8}>
              {/* Booking Date_value */}
              <BookingFieldValue
                label="予約日付："
                labelMinWidth={0}
                value={
                  bookingDetail?.dateBooking &&
                  Helper.formatDayJST(bookingDetail?.dateBooking, 'YYYY年MM月DD日 HH時mm分')
                }
              />
            </Col>
            <Col span={8}>
              {/* Progress status_value */}
              <BookingFieldValue
                label="ステータス："
                labelMinWidth={0}
                value={
                  bookingDetail?.currentStatus?.status &&
                  BOOKING_STATUS_DETAIL[bookingDetail?.currentStatus?.status]
                }
              />
            </Col>
          </Row>
          <Divider className="m-0" />
        </>
      }
      header={{
        title: '予約管理',
      }}
      onBack={() => {
        navigate(
          {
            pathname: '/booking',
            search: `?page=${previousListPage}`,
          },
          { state: { ...listParams } },
        );
      }}
    >
      {/* Customer name */}
      <BookingFieldValue label="お客様名：" value={bookingDetail?.customer?.name} />

      {/* Customer ID_value */}
      <BookingFieldValue label="お客様ID：" value={bookingDetail?.customer?.id} />

      {/* Customer phone number_value */}
      <BookingFieldValue
        label="電話番号："
        value={
          bookingDetail?.customer?.phone &&
          Helper.formatPhoneNumber(`${bookingDetail?.customer?.phone}`)
        }
      />

      {/* Customer gender_value */}
      <BookingFieldValue
        label="お客様の性別："
        value={
          (bookingDetail?.customer.gender || bookingDetail?.customer.gender === 0) &&
          GenderFormat[bookingDetail?.customer.gender]
        }
      />

      {/* Duration_value */}
      <BookingFieldValue
        label="施術時間："
        value={bookingDetail?.menus && getTotalDuration(bookingDetail?.menus)}
      />

      {/* Address_value  */}
      <BookingFieldValue
        label="治療先："
        value={bookingDetail && getBookingAddress(bookingDetail)}
      />

      {/*  Banking_value */}
      <BookingFieldValue
        label="パーキングメモ ："
        value={
          bookingDetail?.parkingNote && (
            <Text className="whitespace-pre-line block text-[#080c35] font-semibold">
              {bookingDetail?.parkingNote}
            </Text>
          )
        }
      />

      {/* Customer_value */}
      <BookingFieldValue
        label="お客様メモ："
        value={
          bookingDetail?.customerNote && (
            <Text className="whitespace-pre-line block text-[#080c35] font-semibold">
              {bookingDetail?.customerNote}
            </Text>
          )
        }
      />

      {/* Therapist full name_value + nickname */}
      <BookingFieldValue
        label="セラピスト名："
        value={`${bookingDetail?.therapist?.fullName} (${bookingDetail?.therapist?.nickName})`}
      />

      {/* Therapist ID_value */}
      <BookingFieldValue label="セラピストID：" value={bookingDetail?.therapist?._id} />

      {/* Therapist phone number_value */}
      <BookingFieldValue
        label="セラピストの電話番号："
        value={
          bookingDetail?.therapist?.phone &&
          Helper.formatPhoneNumber(`${bookingDetail?.therapist?.phone}`)
        }
      />

      {/* Therapist gender_value */}
      <BookingFieldValue
        label="セラピストの性別："
        value={
          (bookingDetail?.therapist.gender || bookingDetail?.therapist.gender === 0) &&
          GenderFormat[bookingDetail?.therapist.gender]
        }
      />

      {/* Cancellation area */}
      {bookingDetail?.currentStatus?.status === BOOKING_LIST_STATUS.CANCELED && (
        <BookingFieldValue
          label="キャンセル情報："
          value={
            (get(bookingDetail, 'cancellingNote.note') || bookingDetail?.cancellationReason) && (
              <Space direction="vertical" size={'middle'}>
                {bookingDetail?.cancellingNote?.note && (
                  <div>
                    <div>理由</div>
                    <div className="font-thin">
                      {bookingDetail?.currentStatus?.reason === REASON.EXPIRED_STANDARD_BOOKING
                        ? '施術開始時間45分前までに承認しなかったため自動的にキャンセルされました'
                        : bookingDetail?.cancellingNote?.note}
                    </div>
                  </div>
                )}
                {bookingDetail?.cancellationReason && (
                  <div>
                    <div>アンケート</div>
                    {get(
                      bookingDetail.cancellationReason,
                      `${bookingDetail.currentStatus.requestBy}`,
                      [],
                    ).map((i: ICancellationData) => (
                      <Fragment key={i.id}>
                        <ul className="font-thin -ml-1 mt-1">
                          {i.reasons.map((j) => (
                            <li key={j}>{j}</li>
                          ))}
                        </ul>
                        {i.note && <p>{i.note}</p>}
                        {i.noteForTherapist && <p>{i.noteForTherapist}</p>}
                      </Fragment>
                    ))}
                  </div>
                )}
              </Space>
            )
          }
        />
      )}

      {/* Menu_value */}
      <Space className="w-full md:max-w-2xl mb-2" direction="vertical">
        <div className="mb-2 text-[#74788b]">メニュー：</div>
        {bookingDetail && (
          <BookingMenu
            bookingMenu={bookingDetail?.menus}
            extensions={bookingDetail?.extensions}
            totalDuration={totalDuration}
            totalPrice={totalPrice}
          />
        )}
      </Space>
      {/* Midnight Fee: Midnight fee > 0 => show the midnight fee */}
      {bookingDetail?.midnightFee && bookingDetail?.midnightFee > 0 && (
        <div className="w-full md:max-w-2xl mb-4">
          <div className="extensions-title mt-2 mb-5">{''}</div>
          <MidNightFee amount={bookingDetail?.midnightFee || 0} />
        </div>
      )}
      {/* Point use value */}
      <div>
        <Space className="w-full md:max-w-2xl mb-2" direction="vertical">
          {bookingDetail?.point?.used?.point && (
            <div>
              <div className="extensions-title mt-4">
                <span>ポイント使用</span>
              </div>
              <Coupon
                amount={bookingDetail?.point?.used?.discount}
                code={`${bookingDetail?.point?.used?.point} ポイント`}
              />
            </div>
          )}
        </Space>
      </div>
      {/* Coupon use value */}
      <div>
        <Space className="w-full md:max-w-2xl mb-2" direction="vertical">
          {bookingDetail?.coupon?.code && (
            <div>
              <div className="extensions-title mt-4">
                <span>利用クーポン</span>
              </div>
              <Coupon amount={bookingDetail?.coupon?.amount} code={bookingDetail?.coupon?.code} />
            </div>
          )}
        </Space>
      </div>

      {/* Payment status area */}
      {bookingDetail && <PaymentStatus selectedBooking={bookingDetail} />}

      {/* Total amount_value */}
      {bookingDetail && (
        <TotalPriceRow
          payment={bookingDetail?.payment}
          point={bookingDetail?.point}
          totalDuration={totalDuration}
          totalPrice={totalPrice}
        />
      )}
    </PageContainer>
  );
};

export default BookingDetail;
