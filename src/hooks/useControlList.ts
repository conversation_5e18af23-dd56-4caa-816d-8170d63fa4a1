import type { QueryKey, UseQueryOptions } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import type { TablePaginationConfig } from 'antd';
import type { FilterValue, SorterResult } from 'antd/es/table/interface';
import type { AxiosRequestConfig, Method } from 'axios';
import type { IListQuery, IListResultService } from 'hooks/types';
import { useMemo, useState } from 'react';
import api from 'utils/api';

import { formatQuery } from './utils';

interface Options<TQueryFnData = unknown, TData = TQueryFnData, TError = unknown>
  extends Omit<
    UseQueryOptions<IListResultService<TQueryFnData>, TError, IListResultService<TData>, unknown[]>,
    'queryFn' | 'queryKey'
  > {
  customParams?: Record<string, unknown>;
  queryKey: QueryKey;
  apiUrl: string;
  method?: Method | string;
  axiosConfig?: AxiosRequestConfig;
}

const useControlList = <
  TQueryFnData = unknown,
  TData = TQueryFnData,
  TError = unknown,
  TObjectSchema = Record<string, unknown>,
>(
  options: Options<TQueryFnData, TData, TError>,
) => {
  const { queryKey, apiUrl, method = 'get', customParams, axiosConfig, ...otherOptions } = options;
  const [listQuery, setListQuery] = useState<IListQuery<TObjectSchema>>({
    page: 1,
    limit: 10,
    ...customParams,
  });

  const onTableChange = (
    params: TablePaginationConfig,
    filter: Record<string, FilterValue | null>,
    sorter: SorterResult<TQueryFnData> | SorterResult<TQueryFnData>[],
  ) => {
    // TODO: Handle multisort
    if (!Array.isArray(sorter)) {
      setListQuery((prevListQuery) => ({
        ...prevListQuery,
        ...filter,
        page: params.current || 1,
        limit: params.pageSize || 10,
        order: sorter.order,
        // Column that has sorter should have key prop
        orderBy: sorter.order ? sorter.columnKey?.toString() : undefined,
      }));
    }
  };

  const onFilter = (values: TObjectSchema | Record<string, unknown>) => {
    setListQuery((prevListQuery) => ({
      ...prevListQuery,
      ...values,
      page: 1,
    }));
  };

  const formattedParams = useMemo(() => formatQuery(listQuery), [listQuery]);

  const { data, isFetching, refetch, isLoading } = useQuery({
    queryKey: queryKey ? [...queryKey, formattedParams] : [apiUrl, formattedParams],
    queryFn: async () => {
      const { data: result }: { data: IListResultService<TQueryFnData> } = await api({
        method,
        params: formattedParams,
        url: apiUrl,
        ...axiosConfig,
      });
      return result;
    },
    keepPreviousData: true,
    ...otherOptions,
  });
  // const pagination: TablePaginationConfig = {
  //   showSizeChanger: true,
  //   pageSize: data?.data?.perPage || 10,
  //   current: data?.data?.page || 1,
  //   total: data?.data?.total || 0,
  //   showTotal: (total, range) => `${range[0]}-${range[1]} of ${total}`,
  // };
  return {
    list: data?.docs || [],
    total: data?.totalDocs || 0,
    totalPages: data?.totalPages || 0,
    page: data?.page || 1,
    perPage: data?.limit || 10,
    isLoading,
    isFetching,
    listQuery,
    refetch,
    onTableChange,
    onFilter,
    setListQuery,
  };
};
export default useControlList;
