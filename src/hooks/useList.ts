import type { QueryKey, UseQueryOptions } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import type { TablePaginationConfig } from 'antd';
import type { AxiosRequestConfig, Method } from 'axios';
import type { IListQuery, IListResult } from 'hooks/types';
import type { ServiceProvider } from 'utils/serviceProvider.conf';

import useRequestAPI from './useRequestAPI';
import useSearch from './useSearch';
import { getSortString, tableQuerySchema } from './utils';

interface Options<TQueryFnData = unknown, TData = TQueryFnData, TError = unknown>
  extends Omit<
    UseQueryOptions<IListResult<TQueryFnData>, TError, IListResult<TData>, unknown[]>,
    'queryFn' | 'queryKey'
  > {
  customParams?: Record<string, unknown>;
  queryKey: QueryKey;
  apiUrl: string;
  method?: Method | string;
  transform?: (params: Record<string, unknown>) => Record<string, unknown>;
  axiosConfig?: AxiosRequestConfig;
  serviceProvider?: ServiceProvider;
}

const useList = <TQueryFnData = unknown, TData = TQueryFnData, TError = unknown>(
  options: Options<TQueryFnData, TData, TError>,
) => {
  const { queryKey, apiUrl, customParams, axiosConfig, transform, ...otherOptions } = options;
  const { query: searchParams, setQuery } = useSearch({ schema: tableQuerySchema });
  const { request } = useRequestAPI({});

  const params = {
    ...searchParams,
    ...customParams, // Do not change the order. Please pass another param if you want to override the params
  };
  const formatParams = (_params: IListQuery<unknown>) => {
    const formattedParams = { ..._params };
    if (formattedParams.orderBy && formattedParams.order) {
      formattedParams.sort = getSortString(formattedParams.orderBy, formattedParams.order);
      delete formattedParams.order;
      delete formattedParams.orderBy;
    }
    if (transform) {
      return transform(formattedParams);
    }
    return formattedParams;
  };

  const formattedParams = formatParams(params);
  const { data, isFetching, refetch, isLoading } = useQuery({
    queryKey: [...queryKey, formattedParams],
    queryFn: async () => {
      const result = await request(apiUrl, 'GET', undefined, formattedParams, axiosConfig);
      return result?.data;
    },
    keepPreviousData: true,
    ...otherOptions,
  });

  const pagination: TablePaginationConfig = {
    showSizeChanger: true,
    pageSize: data?.data.perPage || 10,
    current: data?.data.page,
    total: data?.data.total || 0,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total}`,
  };

  const resetPagination = () => {
    setQuery({ page: 1, limit: 10 });
  };

  return {
    list: data?.data.data || [],
    total: data?.data.total || 0,
    lastPage: data?.data.lastPage || 0,
    page: data?.data.page || 1,
    perPage: data?.data.perPage || 10,
    summary: data?.data?.summary || [],
    isLoading,
    isFetching,
    pagination,
    resetPagination,
    refetch,
  };
};
export default useList;
