import type { Method } from 'axios';
import authQuery from 'models/auth';
import type { IVerifyToken } from 'models/auth/type';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router';

import useMutate from './useMutate';

function useHSBToken(
  url: string = '/account-api/web/salon/verify-token-active',
  methodRequest: Method | string = 'post',
) {
  const { search } = useLocation();
  const navigate = useNavigate();

  const searchParams = new URLSearchParams(search);
  const token = searchParams.get('token') as string;

  const [isLoading, setIsLoading] = useState(true);
  const [isValidToken, setIsValid] = useState(false);
  const [responsePayload, setResponsePayload] = useState<any>({});
  const { mutateAsync: verifyToken } = useMutate<IVerifyToken>(
    authQuery.checkTokenExpired(url, methodRequest),
  );
  useEffect(() => {
    if (token) {
      verifyToken(
        { verifyToken: token },
        {
          onSuccess: (data) => {
            setResponsePayload(data);
            setIsValid(true);
            setIsLoading(false);
          },
          onError: () => {
            setIsValid(false);
            navigate('/url-expired', { replace: true });
          },
        },
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    isLoading,
    isValidToken,
    responsePayload,
    token,
  };
}

export default useHSBToken;
