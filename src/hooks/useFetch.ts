import type { QueryKey, UseQueryOptions } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import type { AxiosRequestConfig } from 'axios';
import type { ServiceProvider } from 'utils/serviceProvider.conf';

import useRequestAPI from './useRequestAPI';

export interface Options<TQueryFnData = unknown, TData = TQueryFnData>
  extends Omit<UseQueryOptions<TQueryFnData, unknown, TData, QueryKey>, 'queryFn' | 'queryKey'> {
  queryKey: QueryKey;
  apiUrl: string;
  customParams?: Record<string, unknown>;
  axiosConfig?: AxiosRequestConfig;
  serviceProvider?: ServiceProvider;
}

const useDetail = <TQueryFnData = unknown, TData = TQueryFnData>(
  options: Options<TQueryFnData, TData>,
) => {
  const { queryKey, apiUrl, customParams, axiosConfig, serviceProvider, ...otherOptions } = options;
  const { request } = useRequestAPI({ serviceProvider });

  const fetchData = async () => {
    const { data } = await request(apiUrl, 'GET', undefined, customParams, axiosConfig);
    return data?.data || data;
  };

  return useQuery({
    queryKey,
    queryFn: fetchData,
    ...otherOptions,
  });
};

export default useDetail;
