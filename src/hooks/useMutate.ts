import type { UseMutationOptions } from '@tanstack/react-query';
import { useMutation } from '@tanstack/react-query';
import { notification } from 'antd';
import type { AxiosRequestConfig, Method } from 'axios';
import { useTranslation } from 'react-i18next';
import type { ServiceProvider } from 'utils/serviceProvider.conf';

import useRequestAPI from './useRequestAPI';

interface Options<TData, TVariables, TError>
  extends Omit<UseMutationOptions<TVariables, TError, TData>, 'mutationFn'> {
  apiUrl: string | ((params: TData) => string);
  method?: Method | string;
  defaultToast?: boolean;
  successMessage?: string;
  axiosConfig?: AxiosRequestConfig;
  serviceProvider?: ServiceProvider;
}

const useMutate = <TData = unknown, TVariables = unknown, TError = unknown>(
  options: Options<TData, TVariables, TError>,
) => {
  const { t } = useTranslation();
  const {
    apiUrl,
    defaultToast,
    method = 'post',
    successMessage,
    axiosConfig,
    serviceProvider,
    ...otherOptions
  } = options;
  const { request } = useRequestAPI({ serviceProvider });

  return useMutation({
    mutationFn: async (params: TData) => {
      const url = typeof apiUrl === 'string' ? apiUrl : apiUrl(params);

      let bodyData;
      let reqParams;
      if (method === 'GET' || method === 'get') {
        bodyData = undefined;
        reqParams = params;
      } else {
        bodyData = params as TData;
        reqParams = undefined;
      }
      const { data } = await request(url, method, bodyData, reqParams, axiosConfig);
      return data?.data;
    },
    onSuccess: () => {
      if (defaultToast || successMessage) {
        notification.success({ message: successMessage || t('validation:completed') });
      }
    },
    ...otherOptions,
  });
};

export default useMutate;
