import type { AxiosRequestConfig, Method } from 'axios';
import { initAPIinstance } from 'utils/abstractAPI';
import { API_URL } from 'utils/config';
import Helper from 'utils/helpers';
import queryClient from 'utils/queryClient';
import type { ServiceProvider } from 'utils/serviceProvider.conf';

const HSB_API = initAPIinstance(API_URL);
HSB_API.interceptors.response.use(
  (response) => {
    if (response?.data?.code && response?.data?.code !== 200) {
      response.status = response?.data?.code;
      if (response?.data?.code === 500) {
        response.data.data = response.data.data
          ? response.data.data
          : { data: response.data.error || 'error' };
      }
      return Promise.reject(response);
    }

    return response;
  },
  ({ response }) => {
    if (response?.data?.code === 401 || response?.status === 401) {
      Helper.removeWebCookie();
      queryClient
        .getQueryCache()
        .findAll(['currentUser'])
        .forEach((query) => query.reset());
      setTimeout(() => {
        window.location.href = '/login';
      });
    }
  },
);
function useRequestAPI({ serviceProvider }: { serviceProvider?: ServiceProvider }) {
  const request = async (
    url: string,
    method: Method | string,
    data: unknown,
    params?: any,
    axiosConfig?: AxiosRequestConfig,
  ) => {
    const webCookie = Helper.getWebCookie();
    const token = webCookie?.token;

    return HSB_API.request({
      ...axiosConfig,
      url,
      data,
      method,
      params,
      baseURL: serviceProvider ? (serviceProvider as unknown as string) : undefined,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  };

  return { request };
}

export default useRequestAPI;
