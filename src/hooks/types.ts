import type { SortOrder } from 'antd/es/table/interface';
import type { SummaryData } from 'models/booking/types';

export interface IListItem {
  id: string;
  name: string;
}

export interface IListResult<T> {
  data: {
    data: T[];
    total: number;
    page: number;
    perPage: number;
    lastPage: number;
    summary?: SummaryData[];
  };
}
export interface IListResultService<T> {
  docs: T[];
  totalDocs: number;
  limit: number;
  hasPrevPage: boolean;
  hasNextPage: boolean;
  page?: number | undefined;
  totalPages: number;
  offset: number;
  prevPage?: number | null | undefined;
  nextPage?: number | null | undefined;
}

export enum SortOderEnum {
  'descend' = 'descend',
  'ascend' = 'ascend',
}
export interface ITableQuery {
  order?: SortOrder | undefined | null;
  orderBy?: string | undefined;
  sort?: string | undefined; // This field is based on your BE requirements. This field is combined between order and orderBy field
  page: number;
  limit: number;
}

export type IListQuery<T> = (T | Record<string, unknown>) & ITableQuery;
export type IOptionItem = {
  label: string;
  value: string;
};
