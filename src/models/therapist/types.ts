export interface Branch {
  areaCode: string;
  name: string;
}

interface CardFee {
  percent: number;
  applyDate: string;
}

export interface Experience {
  name: string;
}

interface Menu {
  [index: number]: string;
}

export interface AreaCode {
  parent: string;
  showingChilds: boolean;
  level: number;
  areaCode: string;
  showing: boolean;
  chossing: boolean;
  name: string;
  children?: AreaCode[];
}

// interface DeparturePoint {
//   name: string;
//   level: number;
//   areaCode: string;
// }

// interface MidnightSettingArea {
//   children?: MidnightSettingArea[];
//   level: number;
//   name: string;
//   parent: string;
//   areaCode: string;
// }

interface MidnightSetting {
  areas: AreaCode[];
  start: string;
  end: string;
}

export interface ProfilePicture {
  privateUrl: string;
  url: string;
}

export interface SummaryResponseRate {
  sumDoneBookings: number;
  approvalRate: number;
  responseRate: number;
  responseTime: string;
}

export type TherapistStatus = 'LOCKED' | 'ACTIVE';
export interface ITherapist {
  _id: string;
  fullName: string;
  phone: string;
  nickName: string;
  email: string;
  branches: Branch[];
  cardFee: CardFee;
  salonId: string;
  isCompletedProfile: boolean;
  isNew: boolean;
  status: TherapistStatus;
  synchronization: boolean;
  createdAt: string;
  updatedAt: string;
  lastLogin: string;
  birthday: string;
  certificate: string;
  experience: Experience;
  gender: number;
  setting: boolean;
  introduction: string;
  step: number;
  menus: Menu;
  areaCodes: AreaCode[];
  availableCodes: string[];
  departurePoint: AreaCode[];
  midnightSetting: MidnightSetting;
  workingAreas: string[];
  scheduleLastUpdateTime: string;
  profilePicture: ProfilePicture;
  lastBookingTime: string;
  specialTreatmentTime: {
    status: boolean;
    duration: number;
  };
  summaryResponseRate: SummaryResponseRate;
}
export type IUser = {
  _id: string;
  role: string;
};
export type IArea = {
  name: string;
  areaCode: string;
};

export type IReviewItem = {
  agentId: string;
  bookingId: string;
  city: IArea;
  ward: IArea;
  comment: {
    therapist: string;
  };
  _id: string;

  createdAt: string;
  customerGender: number;
  dateBooking: string;
  reviewer: {
    birthday: string;
    email: string;
    gender: number;
    name: string;
    phone: string;
    profilePicture?: {
      privateUrl: string;
      url: string;
    };
  };
  therapistGender: number;
  rating?: number;
  url: string;
  updatedAt: string;
};
export type ITherapistReview = {
  detail: {
    count: number;
    rating: number;
  }[];
  reviews: {
    data: IReviewItem[];
    lastPage: number;
    page: number;
    perPage: number;
    total: number;
  };
  summaryReview: {
    createdAt: string;
    sumRating: number;
    sumReviewer: number;
    updatedAt: string;
    user: IUser;
  } | null;
};

interface ImageUrls {
  url: string;
  privateUrl: string;
}

interface Option {
  duration: number;
  originalPrice: number;
  currency: string;
  price: number;
}

interface PresetPrice {
  duration: number;
  originalPrice: number;
  currency: string;
  price: number;
  hash: string;
  extension: {
    maxDuration: number;
    duration: number;
    price: number;
    originalPrice: number;
    currency: string;
  };
}

export interface ITherapistMenu {
  _id: string;
  title: string;
  tags: string[];
  type: string;
  images: {
    icon: ImageUrls;
    small: ImageUrls;
    large: ImageUrls;
  };
  options: Option[];
  presetPrice: PresetPrice;
}

// blacklist customer
export interface IBlackListCustomer {
  _id: string;
  email: string;
  phone: string;
  name: string;
  lastLogin: string;
  lastDoneBookingTime: string;
  lastChargedBookingTime: string;
  lastCanceledBookingTime: string;
  lastBookingTime: string;
}

export interface IBookingOnSlotSchedule {
  bookingId: string;
  category: 'CONFIRMED' | 'REQUEST';
  createdAt: string;
  end: string;
  start: string;
  isBookingEndMark: boolean;
}
export interface ISlotScheduleWorkingTime {
  start: string;
  end: string;
}
// schedule customer
export interface ISlotSchedule {
  bookingIds: string[];
  workingTimes: ISlotScheduleWorkingTime[];
  busyTimeslots: IBookingOnSlotSchedule[];
}

// note - comment
export interface INoteInfo {
  commenter: IUser;
  user: IUser;
  content: string;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  _id: string;
}
