const therapistQuery = {
  create: {
    queryKey: ['therapist', 'create'],
    apiUrl: '/account-api/web/salon/therapist',
  },
  list: {
    queryKey: ['therapist', 'list'],
    apiUrl: '/account-api/web/salon/therapist',
    method: 'get',
  },
  switchSpecialSetting: (therapistId: string) => ({
    queryKey: ['therapist', 'setting'],
    apiUrl: `/account-api/web/salon/therapist/${therapistId}/treatment-setting`,
    method: 'put',
  }),
  profileDetail: (therapistId: string) => ({
    queryKey: ['therapist', 'detail', 'profile', therapistId],
    apiUrl: `/account-api/web/salon/therapist/${therapistId}`,
    method: 'get',
  }),
  getReviewOfTherapist: (therapistId: string) => ({
    queryKey: ['therapist', 'review'],
    apiUrl: `/review/web/salon/review/therapist/${therapistId}`,
    method: 'get',
  }),

  blockTherapist: (therapistId: string) => ({
    queryKey: ['therapist', 'detail', 'block', therapistId],
    apiUrl: `/account-api/web/salon/therapist/${therapistId}/status`,
    method: 'patch',
  }),
  getTherapistMenu: (therapistId: string) => ({
    queryKey: ['therapist', 'detail', 'menu', therapistId],
    apiUrl: `/booking-api/web/salon/therapist/${therapistId}/menus`,
    method: 'get',
  }),
  getTherapistSlotSchedule: (therapistId: string) => ({
    queryKey: ['therapist', 'slot-schedule', therapistId],
    apiUrl: `/matching-api/web/salon/therapist/${therapistId}/timeslots`,
    method: 'get',
  }),
  getBlockedCustomerList: (therapistId: string) => ({
    queryKey: ['therapist', 'detail', 'blackList', therapistId],
    apiUrl: `/account-api/web/salon/therapist/${therapistId}/blacklist`,
    method: 'get',
  }),
  getComment: (therapistId: string, userRole: string) => ({
    queryKey: ['therapist', 'detail', 'comment', 'note', therapistId],
    apiUrl: `/account-api/web/salon/${userRole}/${therapistId}/comment`,
    method: 'get',
  }),
  updateComment: {
    apiUrl: `/account-api/web/salon/comment`,
    method: 'post',
  },
};

export default therapistQuery;
