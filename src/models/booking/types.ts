import type { ITherapist } from 'models/therapist/types';

export interface IPayment {
  type: string;
  extra: Extra;
  amount: number;
  transaction: Transaction;
}
export interface SummaryData {
  _id: 'REQUEST' | 'CONFIRMED' | 'DONE' | 'CANCELED';
  count: number;
}

export interface Option {
  duration: number;
  originalPrice: number;
  currency: string;
  price: number;
}

export interface Images {
  icon: Icon;
  small: Small;
  large: Large;
}
export interface Therapist {
  _id: string;
  fullName: string;
  phone: string;
  experience: Experience;
  gender: number;
  nickName: string;
  isNew: boolean;
  salonId: string;
  avatar: string;
}

export interface Experience {
  name: string;
}

export interface Extra {
  cardId: string;
}

export interface Transaction {
  bookingId: string;
  amount: number;
  currency: string;
  customerId: string;
  type: string;
  status: number;
  history: History[];
  createdAt: string;
  updatedAt: string;
  _id: string;
  extra: Extra3;
}

export interface History {
  type: string;
  amount: number;
  status: number;
  extra: Extra2;
  created: string;
}

export interface Extra2 {
  id: string;
  amount: number;
  source: Source;
  OrderID: string;
  AccessID: string;
  AccessPass: string;
}

export interface Source {
  id: string;
  name: string;
  brand: string;
  exp_month: number;
  exp_year: number;
  last4: string;
}

export interface Extra3 {
  source: Source2;
}

export interface Source2 {
  id: string;
  name: string;
  brand: string;
  exp_month: number;
  exp_year: number;
  last4: string;
}

export interface Customer {
  id: string;
  gender: number;
  name: string;
  phone: string;
}

export interface IBookingMenu {
  _id: string;
  detail?: string;
  extras: string[];
  isPublic: boolean;
  options: Option[];
  tags: string[];
  taxed: boolean;
  tax: number;
  title: string;
  type: string;
  images: Images;
  defaultPresetPrice: string;
  presetPrices: PresetPrice[];
  selectedOption: SelectedOption;
}

export interface Icon {
  url: string;
  privateUrl: string;
}

export interface Small {
  url: string;
  privateUrl: string;
}

export interface Large {
  url: string;
  privateUrl: string;
}

export interface PresetPrice {
  duration: number;
  originalPrice: number;
  currency: string;
  price: number;
  hash: string;
  extension: Extension;
  isMaxForNewTherapist?: boolean;
  isDefault?: boolean;
}

export interface Extension {
  maxDuration: number;
  duration: number;
  price: number;
  originalPrice: number;
  currency: string;
}

export interface SelectedOption {
  duration: number;
  originalPrice: number;
  currency: string;
  price: number;
  extension: Extension2;
}

export interface Extension2 {
  maxDuration: number;
  duration: number;
  price: number;
  originalPrice: number;
  currency: string;
}

export interface Prefecture {
  name: string;
  areaCode: string;
}

export interface City {
  name: string;
  areaCode: string;
}

export interface Ward {
  name: string;
  areaCode: string;
}

export interface District extends Ward {}

export interface CurrentStatus {
  hash: string;
  category: string;
  status: string;
  timestamp: string;
  reason: string;
  prevHash: string;
  requestBy: any;
}

export interface StatusHistory {
  hash: string;
  category: string;
  status: string;
  timestamp: string;
  reason: string;
  prevHash?: string;
  requestBy?: string;
}

export interface ExtensionBooking {
  _id: string;
  extras: any[];
  presetPrices: PresetPrice2[];
  taxed: boolean;
  tax: number;
  detail: any;
  isPublic: boolean;
  title: string;
  tags: string[];
  type: string;
  options: Option2[];
  images: Images2;
  defaultPresetPrice: string;
  selectedExtension: SelectedExtension;
}

export interface PresetPrice2 {
  duration: number;
  originalPrice: number;
  currency: string;
  price: number;
  hash: string;
  extension: Extension4;
  isDefault?: boolean;
}

export interface Extension4 {
  maxDuration: number;
  duration: number;
  price: number;
  originalPrice: number;
  currency: string;
}

export interface Option2 {
  duration: number;
  price: number;
  originalPrice: number;
  currency: string;
}

export interface Images2 {
  icon: Icon2;
  small: Small2;
  large: Large2;
}

export interface Icon2 {
  url: string;
  privateUrl: string;
}

export interface Small2 {
  url: string;
  privateUrl: string;
}

export interface Large2 {
  url: string;
  privateUrl: string;
}

export interface SelectedExtension {
  duration: number;
  originalPrice: number;
  currency: string;
  price: number;
}
export interface ICoupon {
  amount: number;
  code: string;
  transactionId: number;
}
export interface IPoint {
  used?: {
    point: number;
    discount: number;
    isUsed?: boolean;
    referenceId?: string;
  };
  granted?: {
    point: number;
  };
}

export interface IBookingListItem {
  _id: string;
  currentStatus: CurrentStatus;
  therapist: ITherapist;
  customer: Customer;
  payment: IPayment;
  therapistGender: number;
  paymentMethod: string;
  menus: IBookingMenu[];
  prefecture: Prefecture;
  city: City;
  ward: Ward;
  district: District;
  address: string;
  buildingType?: string;
  nameplate?: string;
  buildingDetails?: string;
  accessMethod?: string;
  statusHistory: StatusHistory[];
  dateBooking: string;
  arrivalTime: null;
  finishTime: null;
  type: string;
  cancellingNote: {};
  isNew: boolean;
}
export interface IBooking {
  _id: string;
  therapist: ITherapist;
  payment: IPayment;
  address: string;
  therapistGender: number;
  customer: Customer;
  dateBooking: string;
  menus: IBookingMenu[];
  prefecture: Prefecture;
  city: City;
  ward: Ward;
  createdBy: string;
  createdFrom: string;
  currentStatus: CurrentStatus;
  statusHistory: StatusHistory[];
  type: string;
  expiry: number;
  expiryAssignment: number;
  expiryCancelingBooking: number;
  totalPrice: number;
  duration: number;
  estimateEndBooking: string;
  durationOfExtension: number;
  endBookingWithoutExtension: string;
  matchedAreaCode: string[];
  createdAt: string;
  updatedAt: string;
  startTime: string;
  extensions: ExtensionBooking[];
  isNew: boolean;
  googleAddress?: string;
  parkingNote?: string;
  customerNote?: string;
  midnightFee?: number | null;
  cancellationReason?: {
    customer?: ICancellationData[];
    therapist?: ICancellationData[];
  };
  cancellingNote?: {
    note?: string;
    isCharged?: boolean;
  };
  coupon: ICoupon;
  point: IPoint;
}
export interface ICancellationData {
  reasons: string[];
  note: string;
  noteForTherapist: string | null;
  id: string;
  timestamp: string;
}
