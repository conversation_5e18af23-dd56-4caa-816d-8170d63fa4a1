import api from 'utils/api';

import type { IPostalCodeToAddress } from './type';

const sharingQuery = {
  signUrl: {
    apiUrl: '/media',
    method: 'post',
  },
  prefectures: {
    queryKey: ['prefectures'],
    apiUrl: '/prefectures',
    axiosConfig: {
      baseURL: `${import.meta.env.VITE_AREA_SERVICE_URL}`,
    },
    customParams: {
      limit: 100,
    },
    staleTime: Infinity,
  },
  postalCodeToAddress: (postalCode: string) => ({
    apiUrl: `/postalCodeToAddress/${postalCode}`,
    queryKey: ['prefectures', postalCode],
    meta: {
      notToastError: true,
    },
    staleTime: Infinity,
    queryFn: async () => {
      const { data } = await api<IPostalCodeToAddress>({
        method: 'get',
        baseURL: `${import.meta.env.VITE_AREA_SERVICE_URL}`,
        url: `/postalCodeToAddress/${postalCode}`,
      });
      return data;
    },
  }),
};

export default sharingQuery;
