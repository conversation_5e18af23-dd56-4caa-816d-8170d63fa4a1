import type { Branch, Experience, ProfilePicture } from 'models/therapist/types';

export type IActiveSalon = {
  verifyToken: string;
  password: string;
};

export interface IForgotPasswordRequest {
  email: string;
}
export interface ICheckExpiredLinkRequest {
  token: string;
}
export interface IResetPassword {
  password: string;
  verifyToken: string;
}
export interface IPrefecture {
  iso: string;
  prefecture_kana?: string;
  prefecture_kanji: string;
  prefecture_romaji?: string;
}
export interface IPrefectureItem extends IPrefecture {
  name: string;
}

export interface CountryPhoneInputValue {
  code?: string | undefined;
  phone?: string | string;
  short?: string;
}
export interface IBankSalonInfo {
  userId: string;
  bankId: string;
  bankName: string;
  cardType: string;
  name: string;
  branchId: string;
  branchName: string;
  branchCode: string;
  status: string;
  bankCode: string;
  cardNumber: string;
}
export interface IBankItem {
  _id: string;
  bankCode: string;
  bankNameCode: string;
  bankName: string;
  bankNameHiragana: string;
}
export interface IBankAPIRes {
  docs?: IBankItem[];
}

export interface IBankBranchItem {
  _id: string;
  branchCode: string;
  bankId: string;
  branchNameCode: string;
  branchName: string;
  telephone: string;
  address: string;
  postalCode: string;
  branchNameHiragana: string;
}

export interface IBankBranchAPIRes {
  docs?: IBankBranchItem[];
}

export interface ISalonDetail {
  _id: string;
  name: string;
  email: string;
  // optional
  phone?: string | null;
  address?: string | null;
  building?: string | null;
  city?: string | null;
  prefecture?: {
    iso: string;
    name: string;
  } | null;
  note?: string | null;
  postalCode?: string | null;
  serviceOwnerId: string;
  roleId: string;
  language: string;
  status: string;
  commission: number;
  createdAt: string;
  updatedAt: string;
  lastLogin: string;
  forcedLogoutAt: string;
  role: string;
  resources?: {
    booking: [];
    therapist: [];
    menu: [];
    schedule: [];
    timeslots: [];
    review: [];
    payout: [];
  };
}
export interface IWorkingTime {
  start: string;
  end: string;
  status: number;
}
export interface ISchedule {
  dayOfWeek: number;
  endOfDate: string;
  startOfDate: string;
  therapistId: string;

  workingTime: IWorkingTime[];
}

export interface ITimeSlot {
  bookingIds: [];
  busyTimeslots: [];
  endOfDate: string;
  therapistId: string;
  updatedAt: string;
  workingTimes: IWorkingTime[];
  freeTimeslots: {
    start: string;
    end: String;
  }[];
}

export interface ITherapistScheduleItem {
  _id: string;
  timeslot: ITimeSlot[];
  schedules: ISchedule[];
  experience: Experience;
  certificate: string;
  fullName: string;
  branches: Branch[];
  gender: number;
  phone: string;
  isNew: boolean;
  introduction: string;
  nickName: string;
  specialTreatmentTime: {
    duration: number;
    status: boolean;
  };
  profilePicture: ProfilePicture;
  therapists: {
    page: number;
    lastPage: number;
    perPage: number;
    total: number;
  };
}
export interface IDataSourceItem {
  key: string;
  [dataIndex: number]: boolean;
  therapist: {
    photo: string;
    therapistId: string;
    therapistName: string;
    isNew: boolean;
  };
}
