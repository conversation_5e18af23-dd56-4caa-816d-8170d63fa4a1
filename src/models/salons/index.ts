import { ServiceProvider } from 'utils/serviceProvider.conf';

const salonQuery = {
  activeSalon: {
    apiUrl: '/account-api/web/salon/active-account',
    method: 'post',
  },
  requestForgotPassword: {
    apiUrl: '/account-api/web/salon/forgot-password',
    method: 'patch',
  },

  checkTokenExpired: {
    apiUrl: '/account-api/web/salon/reset-password',
    method: 'patch',
  },

  getPrefectureList: {
    queryKey: ['prefecture'],
    apiUrl: '/api/prefectures',
    method: 'get',
    serviceProvider: ServiceProvider.C2C_AREA_SERVICE,
    staleTime: Infinity,
  },
  getSalonBankInfo: {
    queryKey: ['BankInfo'],
    apiUrl: '/account-api/web/salon/bank',
    method: 'get',
  },
  updateProfile: {
    apiUrl: '/account-api/web/salon/me',
    method: 'put',
  },
  updateBankInfo: {
    apiUrl: '/account-api/web/salon/bank',
    method: 'post',
  },
  getBankList: {
    queryKey: ['Bank', 'Bank_list'],
    apiUrl: '/banks?limit=99999',
    method: 'get',
    staleTime: Infinity,
    serviceProvider: ServiceProvider.C2C_BANK,
  },
  getBankBranchList: (bankId: string) => ({
    queryKey: ['BranchList', bankId],
    apiUrl: `/banks/${bankId}/branches?limit=99999`,
    method: 'get',
    serviceProvider: ServiceProvider.C2C_BANK,
  }),
  // Schedule working time
  getWorkingSchedule: () => ({
    queryKey: ['salon', 'schedule'],
    apiUrl: `/matching-api/web/salon/therapist/timeslots`,
    method: 'get',
  }),
  requestToChangeEmail: {
    queryKey: ['salon', 'email', 'requestToChangeEmail'],
    apiUrl: '/account-api/web/salon/me/email',
    method: 'put',
  },
  changeEmailWithToken: {
    queryKey: ['salon', 'email', 'changeEmailWithToken'],
    apiUrl: '/account-api/web/salon/change-email',
    method: 'post',
  },
  changePassword: {
    queryKey: ['salon', 'password', 'changePassword'],
    apiUrl: '/account-api/web/salon/me/password',
    method: 'put',
  },
  resetPassword: {
    queryKey: ['salon', 'password', 'resetPassword'],
    apiUrl: '/account-api/web/salon/reset-password',
    method: 'post',
  },
};

export default salonQuery;
