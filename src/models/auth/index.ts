import type { Method } from 'axios';

const authQuery = {
  currentUser: {
    queryKey: ['currentUser'],
    apiUrl: '/account-api/web/salon/me',
  },
  login: {
    apiUrl: '/account-api/web/salon/login',
  },
  logout: {
    apiUrl: '/auth/logout',
    method: 'get',
  },
  refreshToken: {
    apiUrl: '/auth/refresh',
  },
  checkTokenExpired: (url: string, methodRequest: Method | string) => ({
    apiUrl: url,
    method: methodRequest || 'post',
  }),
};

export default authQuery;
