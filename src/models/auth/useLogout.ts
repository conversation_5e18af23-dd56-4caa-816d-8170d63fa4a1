import { Modal } from 'antd';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import Helper from 'utils/helpers';
import queryClient from 'utils/queryClient';

const useLogout = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [modal, context] = Modal.useModal();
  const logout = async () => {
    setTimeout(() => Helper.removeWebCookie());
    queryClient
      .getQueryCache()
      .findAll(['currentUser'])
      .forEach((query) => query.reset());
    setTimeout(() => navigate('/login', { replace: true }));
  };

  const logoutWithoutConfirm = async () => {
    setTimeout(() => Helper.removeWebCookie());
    queryClient
      .getQueryCache()
      .findAll(['currentUser'])
      .forEach((query) => query.reset());
    setTimeout(() => navigate('/login', { replace: true }));
  };

  const showConfirmLogout = () => {
    modal.confirm({
      title: t('confirmation:logout.title'),
      onOk: logout,
      content: t('confirmation:logout.message'),
    });
  };
  return { logout: showConfirmLogout, context, logoutWithoutConfirm };
};

export default useLogout;
