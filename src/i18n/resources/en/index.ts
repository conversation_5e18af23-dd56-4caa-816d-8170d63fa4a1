import confirmation from './confirmation';
import validation from './validation';

const en = {
  confirmation,
  validation,
  common: {
    companyName: 'Company Name',
    address: 'location',
    fullName: 'family name',
    tel: 'telephone number',
    email: 'mail address',
    other: 'other',
  },
  global: {
    yes: 'yes',
    no: 'no',
    ok: 'ok',
    save: 'save',
    edit: 'Edit',
    noData: 'no data',
    resendVerifyEmail: 'Remove email email',
    cancel: 'cancel',
    networkError: 'There is no Internet connection.',
    update: 'Update',
    addNew: 'New addition',
    more: 'See more',
    less: 'Close',
    search: 'Search',
    pleaseSelect: 'Please select',
    unregistered: 'Unregistered',
    view: 'View',
    searchSection: 'Search conditions',
    notAssigned: 'Not assigned',
    register: 'Register',
  },
  login: {
    email: 'Mail address',
    emailPlaceholder: '<EMAIL>',
    password: 'Password',
    passwordPlaceholder: 'Please enter your password',
    login: 'Sign in',
  },
  registerAccount: {
    title: 'Account Registration',
    passwordConfirm: 'Password（Confirmed）',
    passwordConfirmPlaceholder: 'Please enter your password again',
  },
  errorText: {
    description:
      'The page you are looking for could not be found looking for could not be found\nPlease try again from the top',
    error: 'Error code: 404',
    back: 'Return to the top to the top',
  },
  sideMenu: {
    dashboard: 'Dashboard',
    salonManagement: 'Salons',
    reservationManagement: 'Reservations',
    therapistManagement: 'Therapists',
  },
  profileMenu: {
    setting: 'Profile settings',
  },
  adminAccount: {
    title: 'User invitation / member management',
    hint: 'By using the additional and management functions of members, you can browse and edit data.\n If you want to delete browsing authority due to retirement, you can delete it by clicking the trash icon.。',
    add: 'New addition',
    email: 'Mail address',
    name: 'Username',
    role: 'Authority',
    createdAt: 'Registered Date',
    edit: 'User editing',
    create: 'New user registration',
    password: 'Password',
    passwordHint: '* If it is blank, the password will not be updated',
    deleteAdmin: 'Do you really want to delete this?',
    status: 'Status',
    statusLabel: 'Situation',
    accountCreate: 'Account registration',
    accountEdit: 'Account editing',
    ownerCompany: 'Client company',
    companyName: 'Corporate name',
    ownerName: 'Name',
    addEc: 'Sign up',
    emailHint:
      '* The same email address cannot be used between the management account and the client account.',
  },
};

export default en;
