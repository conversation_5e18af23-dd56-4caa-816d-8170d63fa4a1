const Validation = {
  // general: {
  permissionDenied: 'Permission Denied.',
  objectNotFound: "Can't find the item.",
  somethingWentWrong: 'Something went wrong.',
  noNetwork: 'インターネット接続がありません。',
  invalidField: '無効な形式です。',
  requiredField: 'この項目は入力必須です。',
  completed: '完了しました。',
  deleteSuccessfully: '削除しました。',
  resendSuccessfully: 'メールを再送信しました',
  passwordRule: 'パスワードは最低8文字以上です',
  // },
  // register: {
  existedEmail: '	このメールアドレスは既に登録されています。',
  // },
  // login: {
  notExistEmail: '正しいメールアドレスを入力してください。',
  invalidEmail: 'メールフォーマットが無効です。',
  notSetFirstPassword: 'パスワードが設定されていません。',
  invalidLogin: 'ログインに失敗しました。メールアドレス、またはパスワードが間違っています',
  notVerifiedEmail: '認証メールをご確認のうえ、本登録を完了してください。',
  // },
  // verifyEmail: {
  verifySuccess: 'メールアドレス認証が完了しました。',
  expiredInvitation: '招待の期限が過ぎています。',
  // },
  // changePassword: {
  invalidCurrentPassword: '現在のパスワードが間違っています。',
  invalidPasswordRule: 'パスワードは8文字以上、半角英数字、英字大小文字を使用してください。',
  sameOldPassword: '現在のパスワードと同じパスワードは設定できません。',
  notMatchedPasswords: 'パスワードが一致しません。',
  notAllowedWhiteSpace: '空白文字は使用できません。',
  changePasswordSuccess: 'パスワードを変更しました。',
  // },
  // profile: {
  invalidBirthday: '有効な生年月日ではありません。',
  invalidPhoneNumber: '正しい電話番号を入力してください。',
  invalidUrl: '有効なURLを入力してください。',
  // form input validation
  requiredSalonName: 'サロン名を入力してください',
  requiredPostCode: '郵便番号を記入してください',
  requiredPrefecture: '都道府県を選択してください',
  requiredCityWard: '市区を入力してください',
  requiredStreetBlock: '町村・番地を入力してください',
  // Bank input form
  requiredBankName: '金融機関名を選択してください',
  requiredBranchName: '支店名を選択してください',
  requiredAccountType: '口座種別を選択してください',
  requiredAccountNumber: '口座番号を入力してください',
  maxLengthAccountNumber: '７桁の番号を入力してください',
  requiredAccountHolderName: '口座名義を入力してください',
  invalidAccountHolderName: 'カタカナで入力してください',

  // },
  // review: {
  submitSuccess: '評価が完了しました。',
  expiredEvaluation: '評価は送信済みです。',
  // },
  changeEmailSuccess: 'メールの変更が成功しました。',
  maxLength: '{{number}} 文字以内で入力してください。',
  numberInteger: 'この項目は入力必須です。半角数字で入力してください。',
  maxField: '最大5つまで選択できます',
};

export default Validation;
