import confirmation from './confirmation';
import validation from './validation';

const jp = {
  confirmation,
  validation,
  common: {
    companyName: '会社名',
    address: '所在地',
    fullName: '氏名',
    phone: '電話番号',
    email: 'メールアドレス',
    other: 'その他',
  },
  global: {
    yes: 'はい',
    no: 'いいえ',
    ok: 'OK',
    save: '保存',
    edit: '編集',
    noData: '検索結果が見つかりません',
    resendVerifyEmail: 'メールを再送',
    cancel: 'キャンセル',
    networkError: 'インターネット接続がありません。',
    update: '更新',
    addNew: '新規追加',
    more: 'もっと見る',
    less: '閉じる',
    search: '検索',
    deleteConfirm: '本当に削除しますか？',
    pleaseSelect: '選択してください',
    unregistered: '未登録',
    view: '確認',
    searchSection: '検索条件',
    notAssigned: '紐付け未設定',
    register: '登録する',
    change: '変更',
  },
  login: {
    email: 'メールアドレス',
    emailPlaceholder: '<EMAIL>',
    password: 'パスワード',
    passwordPlaceholder: ' パスワードを入力してください',
    login: 'ログイン',
  },
  registerAccount: {
    title: 'アカウント登録',
    passwordConfirm: 'パスワード（確認）',
    passwordConfirmPlaceholder: 'もう一度パスワード入力してください',
  },
  forgotPassword: {
    forgotPasswordTitle: 'パスワードの再設定',
    forgotPasswordContent:
      'ご登録いただいたメールアドレスを入力してください。再設定の案内メールを送信します。',
    forgotPasswordPlaceholder: '登録しているメールアドレスを入力してください',
    sendEmail: '送信する',
    newPassword: '新しいパスワード',
    newPasswordPlaceholder: '新しいパスワードを入力してください',
    confirmNewPassword: '新しいパスワード（確認）',
    confirmNewPasswordPlaceHoler: '新しいパスワードをもう一度入力してください',
    resetPassword: 'パスワードを再設定する',
    sentEmailInforTitle: 'パスワード再設定メール送信完了',
    sentEmailInforDescription:
      'ご登録いただいたメールアドレスにパスワード再設定メールを送信致しました。\n メール内のURLをクリックしてパスワードを再設定してください。',
  },
  errorText: {
    description: 'お探しのページが見つかりませんでした\nトップから再度お試しください',
    error: 'エラーコード: 404',
    back: 'トップに戻る',
  },
  sideMenu: {
    dashboard: 'ダッシュボード',
    salonManagement: 'サロン管理',
    reservationManagement: '予約管理',
    therapistManagement: 'セラピスト管理',
  },
  profileMenu: {
    setting: 'プロフィール設定',
  },
  salonSetting: {
    salonID: 'サロンID',
    profileTab: 'プロフィール',
    salonName: 'サロン名 (必須)',
    salonNamePlaceholder: 'ABCサロン',
    postCode: '郵便番号',
    bankInfoTab: '銀行口座',
    prefecture: '都道府県',
    city: '市区',
    townVillage: '町村・番地',
    buildingName: 'ビル名・部屋番号',
    remarks: '備考',
  },
  adminAccount: {
    title: 'ユーザー招待・メンバー管理',
    hint: 'メンバーの追加・管理機能を使うと、データの閲覧・編集が可能となります。\n 退職などで閲覧権限を削除したい場合、ゴミ箱のアイコンをクリックすることで削除できます。',
    add: '新規追加',
    email: 'メールアドレス',
    name: 'ユーザー名',
    role: '権限',
    createdAt: '登録日時',
    edit: 'ユーザー編集',
    create: 'ユーザー新規登録',
    password: 'パスワード',
    passwordHint: '※空欄の場合パスワードは更新されません',
    deleteAdmin: '本当に削除しますか？',
    status: 'ステータス',
    statusLabel: '状態',
    accountCreate: 'アカウント登録',
    accountEdit: 'アカウント編集',
    ownerCompany: 'クライアント企業',
    companyName: '企業名',
    ownerName: '名前',
    addEc: '新規登録',
    fullName: '氏名',
    emailHint: '※ 管理アカウントとクライアントアカウント間で同じメールアドレスは使用できません。',
  },
};

export default jp;
