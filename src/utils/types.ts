export type IError = {
  error: string | { property: string; constraints: Record<string, string> }[];
  data: {
    data: {
      message: string;
      field: string;
      validation: string;
    }[];
    code: number;
    error: string;
  };
  code: number;
  status: number;
  response?: {
    status: number;
    data: {
      code: number;
      error: string;
    };
  };
};

export enum ROLES {
  ADMIN = 'ADMIN',
  EC_OWNER = 'EC_OWNER',
}

export enum AdminRole {
  ADMIN = 'ADMIN',
  OPERATOR = 'OPERATOR', // Corresponding to MEMBER in spec
}

export enum Status {
  INACTIVE = 'INACTIVE',
  ACTIVE = 'ACTIVE',
  DELETED = 'DELETED',
}
