import { deleteCookie, getCookie, setCookie } from 'cookies-next';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { get } from 'lodash';

// A unique ID to identify both uploads and reloaders
// The currently waiting uploads, is uploading if not empty
// All the reloaders to call when the uploading status changed

const Helper = {
  getWebCookie: () => {
    const cookies = JSON.parse((getCookie('hogugu-sb-web-cookie') || null) as string);
    return cookies;
  },
  removeWebCookie: () => deleteCookie(`hogugu-sb-web-cookie`, { path: '/' }),
  setToken: (data: Record<string, string>, remember?: boolean): void =>
    setCookie('hogugu-sb-web-cookie', data, {
      path: '/',
      ...(remember
        ? {
            maxAge: import.meta.env.SESSION_TIME ? Number(import.meta.env.SESSION_TIME) : 2592000, // 30 * 24 * 60 * 60 * 1
          }
        : {}),
    }),
  arrayToString: (array = []): string => array.map((item) => item).join(', '),
  getDownLoadFileCSV: (
    csvContent: number | boolean | BlobPart,
    csvFileName = 'dataList.csv',
  ): void => {
    const exportedFilename = csvFileName;
    const BOM = '\uFEFF';
    const formatCsvContent = BOM + csvContent;
    const blob = new Blob([formatCsvContent], {
      type: `text/csv;charset=utf-8,%EF%BB%BF${encodeURIComponent(formatCsvContent)}`,
    });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      // feature detection
      // Browsers that support HTML5 download attribute
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', exportedFilename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  },
  formatCurrencyJPYName: (value: number | bigint): string => {
    const currencyJPYName = new Intl.NumberFormat('ja-JP', {
      style: 'currency',
      currency: 'JPY',
      currencyDisplay: 'name',
      minimumFractionDigits: 0,
      maximumFractionDigits: 4,
    });
    return currencyJPYName.format(value);
  },

  getInternalUrl: (urlString: string, data: unknown): string | null => {
    const URL_PARAMETER = /:([^/]+)/;
    let url: string | null = null;
    if (urlString) {
      let allFound = true;
      url = urlString.replace(URL_PARAMETER, (_: string, path: string): string => {
        const seg = get(data, path, '');
        if (seg) {
          return seg;
        }
        allFound = false;
        return '';
      });
      if (!allFound) {
        url = null;
      }
    }
    return url;
  },
  convertObjectToOptions: (obj: Record<string | number, string | number>) => {
    return Object.keys(obj).map((key) => ({ _id: key, name: obj[key] || '' }));
  },
  addCommaToString: (value: string | number) => {
    if (!value) {
      return '';
    }
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  },
  formatUrl: (url: string) => {
    if (typeof url !== 'string') {
      return undefined;
    }
    return url.startsWith('https://') || url.startsWith('http://') ? url : `http://${url}`;
  },
  roundRating: (num1?: number, num2?: number) => {
    if (!num1 || !num2) {
      return 0;
    }
    return Math.round((num1 / num2) * 100);
  },
  formatRatePercent: (percent: number) => {
    if (percent === 0) {
      return `${percent}%`;
    }
    return `~${percent}%`;
  },
  convertArrayToEntities: <T>(array: (T & { _id?: string; id?: string })[]) => {
    const ids: string[] = [];
    const entities = (array || []).reduce((acc, cur) => {
      if (cur._id) {
        ids.push(cur._id);
        return { ...acc, [cur._id]: cur };
      }
      if (cur.id) {
        ids.push(cur.id);
        return { ...acc, [cur.id]: cur };
      }
      return acc;
    }, {});
    return {
      ids,
      entities,
    };
  },
  formatPhoneNumber: (phoneNumber: string): string => {
    const japanCountryCode = '+81';
    const modifiedPhoneNumber = phoneNumber.startsWith(japanCountryCode)
      ? phoneNumber.replace(japanCountryCode, '0')
      : phoneNumber;
    return modifiedPhoneNumber;
  },
  formatDayJST: (date: string, template: string) => {
    return dayjs(date)
      .utc()
      .utcOffset(9 * 60)
      .format(template);
  },
  convertToJapaneseTime(input: string | null | undefined): string {
    if (input) {
      return `平均${input}以内`;
    }
    return '平均30分以内';
  },
  // Schedule
  getDataIndexFromIndex: (index: number) => {
    const halfIndex = Math.floor(index / 2);
    if (index % 2 === 0) {
      return halfIndex * 100;
    }
    return halfIndex * 100 + 30;
  },
  padStartTime: (time: Dayjs | number) => time.toString().padStart(2, '0'),
  getIndexFromMoment: (time: Dayjs) => {
    return (
      (dayjs(time).utcOffset('+0900').hour() + dayjs(time).utcOffset('+0900').minute() / 60) * 2
    );
  },
};

export default Helper;
