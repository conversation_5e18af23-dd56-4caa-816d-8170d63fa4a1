/* eslint-disable max-len */
export const INSTALLATION_ID = '@skeleton_installationId';

export const REGEX = {
  PASSWORD_POLICY:
    // eslint-disable-next-line no-useless-escape
    /^(?=.*[a-z]+)(?=.*[A-Z]+)(?=.*[0-9]+)([A-Za-z0-9.!@#$%^&*()_+\-=]{8,100})$/,
  KATAKANA: /^[ｧ-ﾝﾞﾟァ-・ヽヾ゛゜ー()-.（-）]+$/,
  // eslint-disable-next-line no-useless-escape
  URL: /^(https?:\/\/)?((([a-z\d]([a-z\d-]*[a-z\d])*)\.)+[a-z]{2,}|((\d{1,3}\.){3}\d{1,3}))(\:\d+)?(\/[-a-z\d%_.~+]*)*(\?[;&a-z\d%_.~+=-]*)?(\#[-a-z\d_]*)?$/i,
  PASSWORD: /^[a-zA-Z0-9!@#$%^&*-?_]{8,}$/,
  PHONE: /^[0-9]{9,12}$/,
  MIN_8_CHARS: /.{8,}/,
  ZIP_CODE: /[0-9]{3}-[0-9]{4}/,
  EMAIL:
    /^[a-z0-9*+_-]+(?:\.[a-z0-9*+_-]+)*@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(((?![-])[a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
};

export enum DateFormat {
  MONTH_YEAR_SHORT = 'MM/YY',
  YEAR_MONTH_DATE = 'YYYY/MM/DD',
  YEAR_MONTH = 'YYYY/MM',
  YEAR_MONTH_DASH = 'YYYY-MM',
  YEAR_MONTH_DATE_HOUR = 'YYYY/MM/DD HH:mm',
  YEAR_MONTH_DATE_HOUR_DASH = 'YYYY-MM-DD HH:mm',
  HOUR_YEAR_MONTH_DATE = 'HH:mm YYYY-MM-DD',
  HOUR_YEAR_MONTH_DATE_JP = 'HH:mm YYYY年MM月DD日',
  YEAR_MONTH_DATE_HOUR_JP = 'YYYY年MM月DD日 HH:mm',
  YEAR_MONTH_DATE_JP = 'YYYY年MM月DD日',
  YEAR_MONTH_DATE_HOUR_MS = 'YYYY/MM/DD HH:mm:ss',
  MONTH_DATE_HOUR_JP = 'YYYY年MM月DD日 HH時mm分',
  ISO = 'YYYY-MM-DDTHH:mm:ss.sss[Z]',
}

export const GenderFormat: { [key: number]: string } = {
  // Any
  0: 'どちらでも',
  // Male
  1: '男性',
  // Female
  2: '女性',
};

export const BANK_ACCOUNT_TYPE = [
  { _id: '普通', name: '普通' },
  { _id: '定期', name: '定期' },
  { _id: '当座', name: '当座' },
];

export const TYPE_PAYMENT = {
  CASH: 'cash',
  CARD: 'card',
  GCARD: 'gcard',
  VCARD: 'vcard',
};

export const THERAPIST_STATUS = {
  LOCKED: 'LOCKED',
  INACTIVE: 'INACTIVE',
  ACTIVE: 'ACTIVE',
};
