@tailwind base;
@tailwind components;
@tailwind utilities;

*, *:before, *:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.pointer, a {
  cursor: pointer;
}

a {
  text-decoration: none;
}

body {
  margin: 0px;
}
li.ant-menu-item.ant-menu-item-only-child.ant-pro-base-menu-menu-item:not(.ant-menu-item-selected):not(:active):hover {
  color: #fff;
}
.ant-btn-primary:disabled {
  cursor: not-allowed;
  color:#fff;
  background-color:rgb(110, 172, 248);
  box-shadow: none;
}

@layer components {
  .page-title {
    @apply bg-[black] p-3 text-center text-white mb-6 text-lg
  }
}

.ant-pro-layout .ant-pro-sider .ant-layout-sider-children {
  padding-top: 12px;
}

.ant-tabs.ant-tabs-top.ant-pro-page-container-tabs .ant-tabs-nav {
  margin-bottom: 0
}
.ant-modal-confirm .ant-modal-confirm-body {
  justify-content: center;
}
.ant-list-vertical .ant-list-item .ant-list-item-meta .ant-list-item-meta-title {
margin-bottom: 0;
}