{
  "root": true,
  // Configuration for JavaScript files
  "extends": ["airbnb-base", "plugin:prettier/recommended"],
  "rules": {
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",
    "prettier/prettier": [
      "error",
      {
        "singleQuote": true,
        "endOfLine": "auto"
      }
    ]
  },
  "overrides": [
    // Configuration for TypeScript files
    {
      "files": ["**/*.ts", "**/*.tsx"],
      "plugins": ["@typescript-eslint", "unused-imports", "simple-import-sort", "react-hooks"],
      "extends": [
        "plugin:react/recommended",
        "plugin:react-hooks/recommended",
        "airbnb-typescript",
        "plugin:prettier/recommended",
        "plugin:react-hooks/recommended"
      ],
      "parserOptions": {
        "project": "./tsconfig.json",
        "ecmaVersion": 2020
      },
      "rules": {
        "import/extensions": "off",
        "import/no-anonymous-default-export": "warn",
        "react/no-unknown-property": "off",
        "react/react-in-jsx-scope": "off",
        "react/prop-types": "off",
        "prettier/prettier": [
          "error",
          {
            "trailingComma": "all",
            "singleQuote": true,
            "endOfLine": "auto"
          }
        ],
        "react/destructuring-assignment": "off", // Vscode doesn't support automatically destructuring, it's a pain to add a new variable
        "react/require-default-props": "off", // Allow non-defined react props as undefined
        "react/jsx-props-no-spreading": "off", // _app.tsx uses spread operator and also, react-hook-form
        "@typescript-eslint/comma-dangle": "off", // Avoid conflict rule between Eslint and Prettier
        "@typescript-eslint/consistent-type-imports": "error", // Ensure `import type` is used when it's necessary
        "import/prefer-default-export": "off", // Named export is easier to refactor automatically
        "simple-import-sort/imports": "error", // Import configuration for `eslint-plugin-simple-import-sort`
        "simple-import-sort/exports": "error", // Export configuration for `eslint-plugin-simple-import-sort`
        "@typescript-eslint/no-unused-vars": "off",
        "unused-imports/no-unused-imports": "error",
        "unused-imports/no-unused-vars": ["error", { "argsIgnorePattern": "^_" }],
        "no-underscore-dangle": "off",
        "react-hooks/rules-of-hooks": "error",
        "react-hooks/exhaustive-deps": "warn",
        "react/jsx-sort-props": 1,
        "import/no-extraneous-dependencies": ["error", { "devDependencies": true }]
      }
    }
  ]
}
